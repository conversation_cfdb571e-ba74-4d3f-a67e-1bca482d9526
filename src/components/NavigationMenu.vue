<template>
  <!-- Sticky Menu -->
  <nav
    class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50"
  >
    <!-- Left: Icon and Title -->
    <div class="flex items-center space-x-2">
      <!-- Icon: replace emoji with SVG or image if needed -->
      <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-6 h-6">
      <router-link to="/" class="font-bold text-sm md:text-lg text-gray-800 hover:text-blue-600 transition-colors">
        QuestionLegale.Info
      </router-link>
    </div>

    <!-- Mobile Section: Burger and Buttons -->
    <div class="flex items-center md:hidden space-x-3">
      <!-- Buttons: visible on mobile -->
      <button
        v-if="!isAuthenticated"
        type="button"
        @click="$router.push('/login')"
        class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap"
      >
        Se Connecter
      </button>
      <button
        v-if="!isAuthenticated"
        type="button"
        @click="$router.push('/register')"
        class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap"
      >
        Inscription
      </button>
      <!-- Burger menu button -->
      <button
        type="button"
        @click="toggleMobileMenu"
        class="focus:outline-none p-0.5"
        aria-label="Toggle menu"
      >
        <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
    </div>

    <!-- Center: Menu Items (Desktop) -->
    <div class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
      <a href="#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
      <a href="/Articles/index.html" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
      <a href="#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
    </div>

    <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
    <div class="hidden md:flex items-center space-x-2">
      <template v-if="!isAuthenticated">
        <button
          type="button"
          @click="$router.push('/login')"
          class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap"
        >
          Se Connecter
        </button>
        <button
          type="button"
          @click="$router.push('/register')"
          class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap"
        >
          Inscription
        </button>
      </template>
      <template v-else>
        <router-link
          to="/app/restricted"
          class="px-3 py-1 text-gray-700 hover:text-blue-500 transition text-sm whitespace-nowrap"
        >
          Dashboard
        </router-link>
        <router-link
          to="/logout"
          class="px-3 py-1 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition text-sm whitespace-nowrap"
        >
          Déconnexion
        </router-link>
        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
          Crédits: {{ credits }}
        </span>
      </template>
    </div>
  </nav>

  <!-- Mobile Menu: Only for menu items (dropdown) -->
  <div
    v-show="showMobileMenu"
    class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden z-40"
  >
    <div class="flex flex-col space-y-3">
      <a href="#analyse" class="text-gray-700 hover:text-blue-500 font-medium" @click="toggleMobileMenu">
        Question?
      </a>
      <a href="#references" class="text-gray-700 hover:text-blue-500 font-medium" @click="toggleMobileMenu">
        References
      </a>
      <a href="#professionnels" class="text-gray-700 hover:text-blue-500 font-medium" @click="toggleMobileMenu">
        Les Professionnels
      </a>
      <a href="#utiles" class="text-gray-700 hover:text-blue-500 font-medium" @click="toggleMobileMenu">
        Sites Utiles
      </a>
      <a href="#comment" class="text-gray-700 hover:text-blue-500 font-medium" @click="toggleMobileMenu">
        Comment ça marche?
      </a>
    </div>
    
    <!-- Authenticated user menu items in mobile -->
    <div v-if="isAuthenticated" class="flex flex-col space-y-3 mt-4 pt-4 border-t border-gray-200">
      <router-link
        to="/app/restricted"
        class="text-gray-700 hover:text-blue-500 font-medium"
        @click="toggleMobileMenu"
      >
        Dashboard
      </router-link>
      <router-link
        to="/logout"
        class="text-gray-700 hover:text-blue-500 font-medium"
        @click="toggleMobileMenu"
      >
        Déconnexion
      </router-link>
      <span class="text-green-600 font-medium">Crédits: {{ credits }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()
const { isAuthenticated, credits } = storeToRefs(authStore)

const showMobileMenu = ref(false)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
