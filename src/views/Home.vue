<template>
  <div class="home">
    <h1>Bienvenue sur Supavite</h1>
    <div v-if="supabaseStatus.status === 'error'" class="supabase-status error">
      État Supabase : Erreur - {{ supabaseStatus.message }}
    </div>
    <div v-else-if="supabaseStatus.status === 'ok'" class="supabase-status ok">
      État Supabase : OK
    </div>
    <div v-else class="supabase-status loading">
      Vérification de l'état Supabase...
    </div>

    <div class="form-container">
      <h2>Saisir les données</h2>
      <form @submit.prevent="handleSubmit" class="data-form">
        <div class="form-group">
          <label for="data">Données à traiter:</label>
          <textarea
            id="data"
            v-model="formData"
            rows="8"
            class="form-textarea"
            placeholder="Entrez vos données ici..."
            required
            @click="handleTextareaClick"
          ></textarea>
          <div v-if="frenchDetectionMessage" class="french-detection-message error">
            {{ frenchDetectionMessage }}
          </div>
        </div>
        <button type="submit" class="submit-button">
          Envoyer
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useQuestionStore } from '../stores/question'

const router = useRouter()
const authStore = useAuthStore()
const questionStore = useQuestionStore()
const formData = ref('')
const supabaseStatus = ref({ status: 'loading' })
const frenchDetectionMessage = ref('')

// Function to detect French language
const detectFrench = (text) => {
  const frenchWords = [
    'le', 'la', 'les', 'un', 'une', 'des',
    'je', 'tu', 'il', 'elle', 'nous', 'vous',
    'de', 'à', 'en', 'dans', 'sur', 'avec',
    'et', 'mais', 'ou', 'car',
    'être', 'avoir', 'faire', 'dire', 'aller'
  ];

  const frenchGrammarIndicators = [
    'é', 'è', 'ê', 'ë', 'à', 'â', 'î', 'ï', 'ô', 'ù', 'û', 'ç',
    'ais', 'ait', 'ions', 'iez', 'aient',
    '-t-il', '-t-elle', 'qu\'', 'n\'', 'm\'', 't\'', 's\'', 'c\''
  ];

  const lowerText = text.toLowerCase();

  const frenchScore = {
    commonWords: 0,
    accents: 0,
    grammarPatterns: 0
  };

  frenchWords.forEach(word => {
    if (lowerText.includes(word)) {
      frenchScore.commonWords += 1;
    }
  });

  const accentCount = (lowerText.match(/[éèêëàâîïôùûç]/g) || []).length;
  frenchScore.accents = accentCount;

  frenchGrammarIndicators.forEach(pattern => {
    if (lowerText.includes(pattern)) {
      frenchScore.grammarPatterns += 1;
    }
  });

  const totalScore =
    frenchScore.commonWords * 2 +
    frenchScore.accents * 1.5 +
    frenchScore.grammarPatterns * 3;

  return {
    isLikelyFrench: totalScore > 5,
    score: totalScore,
    details: frenchScore
  };
};

// Handle textarea click - redirect to login if not authenticated
const handleTextareaClick = () => {
  if (!authStore.isAuthenticated) {
    router.push({
      path: '/login',
      query: {
        redirect: '/'
      }
    })
  }
}

// Handle form submission
const handleSubmit = () => {
  if (!authStore.isAuthenticated) {
    router.push({
      path: '/login',
      query: {
        redirect: '/app/reponse-legale'
      }
    })
    return
  }

  // Check if the input text is French
  const detectionResult = detectFrench(formData.value);
  if (!detectionResult.isLikelyFrench) {
    frenchDetectionMessage.value = 'Le texte saisi ne semble pas être en français. Veuillez saisir votre question en français.';
    return;
  } else {
    frenchDetectionMessage.value = ''; // Clear message if it was previously set
  }

  // Store the question text in the question store
  questionStore.setQuestion(formData.value)

  // Navigate to the response page
  router.push({
    name: 'ReponseLegale'
  })
}

onMounted(async () => {
  try {
    supabaseStatus.value = await authStore.testSupabaseConnection()
  } catch (error) {
    supabaseStatus.value = {
      status: 'error',
      message: 'Erreur de connexion au serveur'
    }
  }
})
</script>

<style scoped>
.form-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
}

.home {
  padding: 2rem;
  text-align: center;
}

.supabase-status {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
}

.supabase-status.error {
  color: white;
  background-color: #dc3545; /* Red color for error */
}

.supabase-status.ok {
  color: white;
  background-color: #28a745; /* Green color for OK */
}

.supabase-status.loading {
  color: black;
  background-color: #ffc107; /* Yellow color for loading */
}

.data-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
}

.form-textarea:focus {
  outline: none;
  border-color: #42b983;
  box-shadow: 0 0 0 2px rgba(66, 185, 131, 0.1);
}

.submit-button {
  align-self: flex-start;
  padding: 0.75rem 1.5rem;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #3aa876;
}

.submit-button:active {
  background-color: #359469;
}

.french-detection-message {
  margin-top: 0.5rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: bold;
  color: white;
  background-color: #dc3545; /* Red color for error */
  text-align: left;
}
</style>
