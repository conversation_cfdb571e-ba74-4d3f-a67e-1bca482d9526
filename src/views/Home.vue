<template>
  <div class="home bg-gray-200 text-gray-800">
    <!-- Hero Section -->
    <HeroSection />

    <!-- Question Form Section -->
    <QuestionForm />

    <!-- Feature Cards -->
    <div class="flex justify-center">
      <FeatureCards />
    </div>

    <!-- How It Works Section -->
    <HowItWorks />

    <!-- FAQ Section -->
    <div class="max-w-7xl mx-auto px-8 md:px-12 py-6">
      <FAQ />
    </div>

    <!-- Frequent Questions Section -->
    <FrequentQuestions />

    <!-- Supabase Status (for debugging, can be hidden in production) -->
    <div v-if="showDebugInfo" class="fixed bottom-4 right-4 z-50">
      <div v-if="supabaseStatus.status === 'error'" class="supabase-status error">
        État Supabase : Erreur - {{ supabaseStatus.message }}
      </div>
      <div v-else-if="supabaseStatus.status === 'ok'" class="supabase-status ok">
        État Supabase : OK
      </div>
      <div v-else class="supabase-status loading">
        Vérification de l'état Supabase...
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'

// Import components
import HeroSection from '../components/HeroSection.vue'
import QuestionForm from '../components/QuestionForm.vue'
import FeatureCards from '../components/FeatureCards.vue'
import HowItWorks from '../components/HowItWorks.vue'
import FAQ from '../components/FAQ.vue'
import FrequentQuestions from '../components/FrequentQuestions.vue'

const authStore = useAuthStore()
const supabaseStatus = ref({ status: 'loading' })
const showDebugInfo = ref(false) // Set to true for debugging

// Initialize Supabase connection status on mount

onMounted(async () => {
  try {
    supabaseStatus.value = await authStore.testSupabaseConnection()
  } catch (error) {
    supabaseStatus.value = {
      status: 'error',
      message: 'Erreur de connexion au serveur'
    }
  }
})
</script>

<style scoped>
.home {
  min-height: 100vh;
}

.supabase-status {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.875rem;
}

.supabase-status.error {
  color: white;
  background-color: #dc3545;
}

.supabase-status.ok {
  color: white;
  background-color: #28a745;
}

.supabase-status.loading {
  color: black;
  background-color: #ffc107;
}
</style>
