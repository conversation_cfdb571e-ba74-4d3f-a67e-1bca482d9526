<template>
  <nav class="navbar">
    <router-link to="/" class="nav-brand">Supavite</router-link>
    <div class="nav-links">
      <a href="/Articles/index.html">Articles</a>
      <template v-if="!isAuthenticated">
        <router-link to="/login">Connexion</router-link>
        <router-link to="/register">S'inscrire</router-link>

      </template>
      <template v-else>
        <router-link to="/app/dashboard">Tableau de bord</router-link>
        <router-link to="/app/restricted">Zone restreinte</router-link>
        <router-link to="/app/question">Poser une question</router-link>
        <router-link to="/logout">Déconnexion</router-link>
        <span :class="['nav-credits', { 'credits-zero': credits <= 0 }]">Crédits: {{ credits }}</span>
      </template>
    </div>
  </nav>
  <main>
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </main>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()
const { isAuthenticated, credits } = storeToRefs(authStore)

import { onMounted } from 'vue';

onMounted(() => {
  authStore.fetchProfile();
});
</script>

<style scoped>
.navbar {
  padding: 1rem 2rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: bold;
  color: #42b983;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-links a {
  color: #2c3e50;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.nav-links a:hover {
  background: #f5f5f5;
}

.nav-links a.router-link-active {
  color: #42b983;
  font-weight: 500;
}

.nav-credits {
  color: #42b983;
  font-weight: 500;
  padding: 0.5rem 1rem;
  background: #f0fdfa;
  border-radius: 4px;
  border: 1px solid #b2f2bb;
}

.nav-credits.credits-zero {
  color: #d9534f; /* Red */
  background-color: #f2dede; /* Light red background */
  border-color: #ebccd1; /* Reddish border */
}

main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
