<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestionLegale.info: Trouvez des réponses juridiques en France</title>
    <meta name="description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="justice France, démarches juridiques, tribunal, droits, procédures judiciaires,
         QuestionLegale, Reponses Juridiques, Avocats, Huissiers, Notaires, Questions Legales, Reponses Juridiques, Avocats, Huissiers, Notaires">

    <meta property="og:title" content="QuestionLegale.info: Trouvez des réponses juridiques en France">
    <meta property="og:description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta property="og:image" content="img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="img/favicons/rowserconfig.xml">
    <meta name="msapplication-TileImage" content="img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="img/favicons/logo.png">


    <style>
        /* Custom styles */


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }


        .presentation-card {
            background-image: url('img/QuestionLegaleHero.jpeg');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }


        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }
    </style>
</head>


<body class="bg-gray-200 text-gray-800">

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <div class="flex items-center space-x-2">
            <!-- Icon: replace emoji with SVG or image if needed -->
            <img src="img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
            <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>
        </div>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <button type="button" id="mobileSignIn"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button" id="mobileSignUp"
                class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap">
                Inscription
            </button>
            <!-- Burger menu button -->
            <button type="button" id="burgerBtn" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="Articles/index.htm"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <button type="button" id="signInBtn"
                class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button" id="signUpBtn"
                class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap">
                Inscription
            </button>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="#analyse" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question?</a>
            <a href="#references" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="#professionnels" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Les Professionnels</a>
            <a href="#utiles" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Sites Utiles</a>
            <a href="#Comment ca marche?" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Comment ca marche?</a>
        </div>
        <!-- Buttons in mobile menu -->
        <div class="flex space-x-3 mt-4">
            <button type="button"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button"
                class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Inscription
            </button>
        </div>
    </div>

    <!-- Presentation -->
    <section id="home" class="relative h-[75vh] mb-8 overflow-hidden">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <div class="relative z-10 flex flex-col md:flex-row h-full pt-16 md:pt-14">
            <!-- Left: Information panel -->
            <div class="w-full md:w-2/3 flex flex-col justify-start px-8 md:px-12 mt-4 md:mt-8" data-aos="fade-right"
                data-aos-duration="800">
                <div class="bg-white bg-opacity-75 backdrop-blur-sm rounded-xl shadow-xl p-6 md:p-8">
                    <h1 class="text-3xl font-extrabold sm:text-4xl text-black mb-4">
                        Posez votre question legale ou juridique
                        <strong class="text-xl md:text-2xl block font-extrabold text-red-600 mt-2">
                            Reponse immediate, anonyme et gratuite(*)
                        </strong>
                    </h1>

                    <div class="flex items-start mt-4">
                        <img src="img/logos/logorobot8.png" alt="Robot Icon" class="w-24 h-24 mr-4 flex-shrink-0">
                        <div>
                            <p class="text-base text-black md:text-lg mb-2">
                                <b>QuestionLegale.Info</b> met à votre disposition des technologies d'intelligence
                                artificielle (LLM) de pointe pour répondre instantanément à vos questions juridiques et légales.
                            </p>

                            <p class="text-base text-black md:text-lg mb-2">
                                Grâce à notre plateforme, obtenez des réponses rapides et précises, et bénéficiez d'une
                                orientation
                                vers les professionnels les plus adaptés à votre situation.
                            </p>

                            <p class="text-base text-black md:text-lg">
                                De plus, QuestionLegale.Info vous aide à constituer un pré-dossier complet,
                                facilitant ainsi votre premier contact avec un expert juridique.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right: Vertical list of benefits -->
            <div class="w-full md:w-1/3 flex flex-col justify-start px-8 md:px-12 mt-4 md:mt-8" data-aos="fade-left"
                data-aos-duration="800" data-aos-delay="200">
                <div class="bg-black bg-opacity-50 backdrop-blur-sm rounded-xl shadow-xl p-6 md:p-8 text-white">
                    <h2 class="text-lg md:text-xl font-bold mb-3">Pourquoi nous choisir ?</h2>

                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Réponses instantanées basées sur le droit français</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Faire une analyse rapide de votre question</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Constitution d'un pré-dossier juridique </span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Identifier les professionnels pertinant et
                                qualifiés</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Accès à des ressources juridiques fiables</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-400 mt-0.5 mr-2 text-sm"></i>
                            <span class="text-sm md:text-base">Aucun stockoage des données</span>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    </section>


    <!-- Search Section -->
    <section id="analyse" class="py-16 bg-gradient-to-b from-gray-100 to-white relative overflow-hidden"
        data-aos="fade-up">

        <div class="max-w-12xl mx-auto px-8 md:px-14 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-10" data-aos="fade-down" data-aos-duration="800">

                <h2 class="text-3xl font-bold text-gray-800 mb-2">Posez votre question juridique ou legale</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Obtenez une réponse immédiate et personnalisée à votre
                    question juridique</p>
            </div>

            <div class="flex flex-col items-center gap-4">
                <!-- Search Box -->
                <div class="w-full max-w-6xl bg-white shadow-xl rounded-xl p-6 md:p-8 text-left" data-aos="zoom-in"
                    data-aos-duration="1000" data-aos-delay="200">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-question text-red-600"></i>
                        </div>
                        Posez votre question (réponse immédiate)
                    </h3>

                    <div class="w-full mt-6">
                        <!-- Animated Gradient Border Textarea with Word Limit -->
                        <div class="gradient-border-outer w-full max-w-none" id="borderWrapper">
                            <div class="gradient-border-inner w-full">
                                <textarea id="questionTextarea" class="custom-textarea"
                                    placeholder="Saisissez votre question juridique ici... Par exemple: 'Comment contester une amende?' ou 'Quels sont mes droits en cas de licenciement?'"
                                    aria-label="Saisie de question juridique"></textarea>
                                <div class="textarea-buttons" role="group" aria-label="Actions de la zone de texte">
                                    <button type="button"
                                        class="bg-blue-500 hover:bg-blue-600 w-8 h-8 flex items-center justify-center rounded-full text-white transition"
                                        title="Pièce jointe" onclick="alert('Pièce jointe cliquée')"
                                        aria-label="Pièce jointe">
                                        <i class="fa fa-paperclip" aria-hidden="true"></i>
                                    </button>
                                    <button type="button"
                                        class="bg-red-600 hover:bg-red-700 w-32 h-8 flex items-center justify-center rounded-full text-white transition space-x-2 shadow-md"
                                        title="Rechercher" onclick="alert('Recherche cliquée')" aria-label="Rechercher">
                                        <i class="fa fa-search"></i>
                                        <span>Analyse</span>
                                    </button>
                                </div>
                                <div id="wordCount" class="word-count">Mots: 0/500</div>
                                <div id="infoMessage" class="info-message">Limite de mots atteinte (500 mots maximum).
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row justify-between items-start md:items-center w-full mt-6">
                        <div class="text-red-600 font-medium flex items-center mb-4 md:mb-0">
                            <i class="fas fa-shield-alt mr-2"></i>
                            <span>Vos données sont protégées et ne sont pas stockées</span>
                        </div>
                    </div>

                    <!-- Styles for the animated gradient border textarea -->
                    <style>
                        .gradient-border-outer {
                            width: 100%;
                            max-width: none;
                            border-radius: 0.75rem;
                            padding: 3px;
                            background: linear-gradient(270deg, #ef4444, #3b82f6, #8b5cf6, #ef4444);
                            background-size: 400% 400%;
                            animation: gradient-animate 2s linear infinite;
                            position: relative;
                            box-sizing: border-box;
                            transition: transform 0.25s cubic-bezier(.4, 2, .6, 1), box-shadow 0.25s, background 0.25s;
                        }

                        .gradient-border-outer.zoomed {
                            transform: scale(1.03);
                            box-shadow: 0 0 0 8px rgba(220, 38, 38, 0.10);
                            background: transparent !important;
                            animation: none !important;
                            border: 3px solid #dc2626;
                            padding: 0;
                        }

                        .gradient-border-inner {
                            background: #fff;
                            border-radius: 0.75rem;
                            width: 100%;
                            position: relative;
                            width: 100%;
                            height: 100%;
                            box-sizing: border-box;
                            min-height: 300px;
                        }

                        .custom-textarea {
                            width: 100%;
                            padding: 1.5rem 1rem 4.5rem 1rem;
                            border-radius: 0.75rem;
                            border: none !important;
                            outline: none !important;
                            background: transparent;
                            color: #374151;
                            resize: vertical;
                            font-size: 1.25rem;
                            box-sizing: border-box;
                            display: block;
                            z-index: 2;
                            position: relative;
                        }

                        .custom-textarea:focus {
                            border: none !important;
                            outline: none !important;
                        }

                        .textarea-buttons {
                            position: absolute;
                            right: 1.5rem;
                            bottom: 1.5rem;
                            display: flex;
                            gap: 0.5rem;
                            z-index: 3;
                            pointer-events: auto;
                        }

                        .word-count {
                            position: absolute;
                            left: 1.5rem;
                            bottom: 1.5rem;
                            font-size: 1.1rem;
                            color: #666;
                            z-index: 3;
                            background: rgba(255, 255, 255, 0.85);
                            padding: 0.25rem 0.5rem;
                            border-radius: 0.5rem;
                            pointer-events: none;
                            user-select: none;
                        }

                        .info-message {
                            position: absolute;
                            left: 50%;
                            bottom: 5rem;
                            transform: translateX(-50%);
                            background: #fee2e2;
                            color: #b91c1c;
                            padding: 0.5rem 1rem;
                            border-radius: 0.5rem;
                            font-size: 1rem;
                            z-index: 10;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
                            display: none;
                            pointer-events: none;
                            user-select: none;
                        }

                        .info-message.show {
                            display: block;
                            animation: fadeIn 0.2s;
                        }

                        @keyframes gradient-animate {
                            0% {
                                background-position: 0% 50%;
                            }

                            100% {
                                background-position: 100% 50%;
                            }
                        }

                        @keyframes fadeIn {
                            from {
                                opacity: 0;
                            }

                            to {
                                opacity: 1;
                            }
                        }
                    </style>

                    <!-- Script for word count and limit -->
                    <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            const textarea = document.getElementById('questionTextarea');
                            const wordCountDiv = document.getElementById('wordCount');
                            const borderWrapper = document.getElementById('borderWrapper');
                            const infoMessage = document.getElementById('infoMessage');
                            const WORD_LIMIT = 500;

                            function getWords(val) {
                                return val.trim().split(/\s+/).filter(Boolean);
                            }

                            function updateWordCountAndLimit() {
                                let value = textarea.value;
                                let words = getWords(value);

                                if (words.length > WORD_LIMIT) {
                                    textarea.value = words.slice(0, WORD_LIMIT).join(' ');
                                    words = getWords(textarea.value);
                                }

                                wordCountDiv.textContent = `Mots: ${words[0] ? words.length : 0}/${WORD_LIMIT}`;
                                if (words.length >= WORD_LIMIT) {
                                    infoMessage.classList.add('show');
                                } else {
                                    infoMessage.classList.remove('show');
                                }
                            }

                            // Prevent further input if word limit is reached
                            textarea.addEventListener('keydown', function (e) {
                                const allowedKeys = [
                                    'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Tab', 'Home', 'End', 'Control', 'Meta', 'Alt'
                                ];
                                let words = getWords(textarea.value);

                                // If at limit, block input unless it's a control key or editing
                                if (
                                    words.length >= WORD_LIMIT &&
                                    !allowedKeys.includes(e.key) &&
                                    // Allow selection replacement
                                    !(textarea.selectionStart !== textarea.selectionEnd)
                                ) {
                                    // Prevent input (except navigation/editing keys)
                                    e.preventDefault();
                                    infoMessage.classList.add('show');
                                }
                            });

                            textarea.addEventListener('input', updateWordCountAndLimit);

                            // Hide info message after a short delay when not at limit
                            textarea.addEventListener('input', function () {
                                let words = getWords(textarea.value);
                                if (words.length < WORD_LIMIT) {
                                    setTimeout(() => infoMessage.classList.remove('show'), 1000);
                                }
                            });

                            // Zoom and border color logic
                            textarea.addEventListener('focus', () => {
                                borderWrapper.classList.add('zoomed');
                            });
                            textarea.addEventListener('blur', () => {
                                borderWrapper.classList.remove('zoomed');
                                setTimeout(() => infoMessage.classList.remove('show'), 300);
                            });

                            // Initial word count
                            updateWordCountAndLimit();
                        });
                    </script>
                </div>

                <!-- Features -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl mt-6">
                    <!-- Feature 1 -->
                    <div class="bg-white rounded-xl p-5 shadow-md flex items-start" data-aos="fade-up"
                        data-aos-delay="300" data-aos-duration="800">
                        <div
                            class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fas fa-bolt text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Réponse immédiate</h4>
                            <p class="text-sm text-gray-600">Obtenez une réponse en quelques secondes</p>
                        </div>
                    </div>

                    <!-- Feature 2 -->
                    <div class="bg-white rounded-xl p-5 shadow-md flex items-start" data-aos="fade-up"
                        data-aos-delay="400" data-aos-duration="800">
                        <div
                            class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">Fiabilité garantie</h4>
                            <p class="text-sm text-gray-600">Basé sur le droit français en vigueur</p>
                        </div>
                    </div>

                    <!-- Feature 3 -->
                    <div class="bg-white rounded-xl p-5 shadow-md flex items-start" data-aos="fade-up"
                        data-aos-delay="500" data-aos-duration="800">
                        <div
                            class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fas fa-user-shield text-purple-600"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-1">100% confidentiel</h4>
                            <p class="text-sm text-gray-600">Aucune donnée n'est conservée</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- About/How It Works Section -->
    <section id="comment" class="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden"
        data-aos="fade-up">
        <div class="max-w-7xl mx-auto px-8 md:px-12 py-6 relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-16" data-aos="fade-down" data-aos-duration="800">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Comment ça marche ?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Notre plateforme vous offre un moyen simple et
                    efficace d'obtenir des réponses à vos questions juridiques en seulement trois étapes.</p>
            </div>

            <!-- Linear Process Visualization -->
            <div id="linear-process" class="w-full h-[200px] mb-16 bg-white rounded-xl " data-aos="fade-up"
                data-aos-delay="150" data-aos-duration="1000"></div>

            <!-- Call to Action -->
            <div class="text-center mt-12" data-aos="zoom-in" data-aos-delay="500" data-aos-duration="800">
                <a href="#services"
                    class="inline-flex items-center px-8 py-3 bg-blue-700 text-white font-medium rounded-full hover:bg-blue-800 transition-colors duration-300 shadow-md">
                    <span>Essayer maintenant</span>
                    <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
            <script>
                // Wait for the DOM to load
                document.addEventListener('DOMContentLoaded', function () {
                    // Set up the margin, width, and height for our SVG
                    const margin = { top: 40, right: 40, bottom: 40, left: 40 };
                    let width = document.getElementById('linear-process').clientWidth - margin.left - margin.right;
                    let height = document.getElementById('linear-process').clientHeight - margin.top - margin.bottom;

                    // Create the SVG element
                    const svg = d3.select("#linear-process")
                        .append("svg")
                        .attr("width", width + margin.left + margin.right)
                        .attr("height", height + margin.top + margin.bottom)
                        .append("g")
                        .attr("transform", `translate(${margin.left}, ${margin.top})`);

                    // Calculate positions based on available width
                    const iconSpacing = width / 4;
                    const icons = [
                        { id: 1, x: iconSpacing, y: height / 2, icon: "Inscription", iconType: "fa-user-plus" },
                        { id: 2, x: iconSpacing * 2, y: height / 2, icon: "Poser votre question", iconType: "fa-question-circle" },
                        { id: 3, x: iconSpacing * 3, y: height / 2, icon: "Visualiser la réponse", iconType: "fa-file-alt" }
                    ];

                    const arrows = icons.slice(0, -1).map((icon, index) => ({
                        id: index + 1,
                        x1: icon.x + 25,
                        y1: icon.y,
                        x2: icons[index + 1].x - 25,
                        y2: icons[index + 1].y
                    }));

                    // Add the dotted lines (arrows)
                    svg.selectAll("path")
                        .data(arrows)
                        .enter()
                        .append("path")
                        .attr("d", d => `M${d.x1},${d.y1} L${d.x2},${d.y2}`)
                        .attr("stroke", "#6B7280")
                        .attr("stroke-width", 2)
                        .attr("stroke-dasharray", "5,5")
                        .attr("fill", "none");

                    // Add arrowheads
                    svg.selectAll("polygon")
                        .data(arrows)
                        .enter()
                        .append("polygon")
                        .attr("points", d => `${d.x2 - 10},${d.y2 - 5} ${d.x2},${d.y2} ${d.x2 - 10},${d.y2 + 5}`)
                        .attr("fill", "#6B7280");

                    // Add the circles (icons)
                    const circles = svg.selectAll(".icon-circle")
                        .data(icons)
                        .enter()
                        .append("g")
                        .attr("class", "icon-circle")
                        .attr("transform", d => `translate(${d.x},${d.y})`);

                    circles.append("circle")
                        .attr("r", 20)
                        .attr("fill", (d, i) => i % 2 === 0 ? "#ce2029" : "#1d4ed8") // Alternate red and blue
                        .attr("cursor", "pointer")
                        .on("mouseover", function (event, d) {
                            const color = d.id % 2 === 0 ? "#1e40af" : "#dc2626";
                            d3.select(this)
                                .transition()
                                .duration(200)
                                .attr("r", 22)
                                .attr("fill", color);
                        })
                        .on("mouseout", function (event, d) {
                            const color = d.id % 2 === 0 ? "#1d4ed8" : "#ce2029";
                            d3.select(this)
                                .transition()
                                .duration(200)
                                .attr("r", 20)
                                .attr("fill", color);
                        });

                    // Add labels below circles
                    circles.append("text")
                        .attr("y", 35)
                        .attr("text-anchor", "middle")
                        .attr("fill", "#374151")
                        .style("font-family", "system-ui, -apple-system, sans-serif")
                        .style("font-size", "14px")
                        .style("font-weight", "500")
                        .text(d => d.icon);

                    // Add step numbers inside circles
                    circles.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", "0.35em")
                        .attr("fill", "white")
                        .style("font-family", "system-ui, -apple-system, sans-serif")
                        .style("font-size", "16px")
                        .style("font-weight", "bold")
                        .text(d => d.id);

                    // Handle window resize
                    function handleResize() {
                        // Update width based on new container size
                        width = document.getElementById('linear-process').clientWidth - margin.left - margin.right;

                        // Update SVG size
                        d3.select("#linear-process svg")
                            .attr("width", width + margin.left + margin.right);

                        // Recalculate positions
                        const newIconSpacing = width / 4;

                        // Update icon positions
                        circles.attr("transform", (d, i) => `translate(${newIconSpacing * (i + 1)},${height / 2})`);

                        // Update arrow positions
                        const newArrows = icons.slice(0, -1).map((icon, index) => ({
                            id: index + 1,
                            x1: newIconSpacing * (index + 1) + 25,
                            y1: height / 2,
                            x2: newIconSpacing * (index + 2) - 25,
                            y2: height / 2
                        }));

                        svg.selectAll("path")
                            .data(newArrows)
                            .attr("d", d => `M${d.x1},${d.y1} L${d.x2},${d.y2}`);

                        svg.selectAll("polygon")
                            .data(newArrows)
                            .attr("points", d => `${d.x2 - 10},${d.y2 - 5} ${d.x2},${d.y2} ${d.x2 - 10},${d.y2 + 5}`);
                    }

                    // Add resize listener
                    window.addEventListener('resize', handleResize);
                });
            </script>

            <!-- FAQ Section -->
            <div class="mt-16" data-aos="fade-up">
                <h3 class="text-2xl font-bold text-center mb-10">Questions fréquemment posées</h3>

                <div class="max-w-4xl mx-auto divide-y divide-gray-200 rounded-lg bg-gray-50 shadow-sm">
                    <!-- FAQ Item 1 -->
                    <div class="p-4 hover:bg-gray-100 transition-colors duration-200 rounded-t-lg">
                        <button class="flex justify-between items-center w-full text-left" onclick="toggleFaq('faq1')">
                            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                                <i class="fas fa-shield-alt mr-3 text-red-600"></i>Mes données sont-elles protégées ?
                            </h4>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform" id="faq1-icon"></i>
                        </button>
                        <div id="faq1" class="mt-3 text-gray-600 hidden">
                            <p class="mb-2">Nous ne conservons aucune donnée autre que celles strictement nécessaires à
                                votre identification. Ni vos questions ni les réponses générées ne sont stockées sur nos
                                serveurs.</p>

                            <p class="mb-2">Votre confidentialité est notre priorité absolue. Pour garder une trace de
                                votre consultation juridique, nous vous recommandons de télécharger votre fiche de
                                réponse en utilisant le bouton prévu à cet effet.</p>

                            <p>Une fois votre session terminée, toutes les données liées à votre consultation sont
                                définitivement effacées de notre système.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="p-4 hover:bg-gray-100 transition-colors duration-200">
                        <button class="flex justify-between items-center w-full text-left" onclick="toggleFaq('faq2')">
                            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                                <i class="fas fa-robot mr-3 text-red-600"></i>Comment fonctionne QuestionLegale.Info ?
                            </h4>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform" id="faq2-icon"></i>
                        </button>
                        <div id="faq2" class="mt-3 text-gray-600 hidden">
                            <p class="mb-2">QuestionLegale.Info utilise l'état de l'art en matière de traitement du
                                langage naturel (LLM) et d'intelligence artificielle. Notre moteur de réponse est
                                régulièrement mis à jour pour intégrer les dernières avancées technologiques.</p>

                            <p class="mb-2">Grâce à ces technologies de pointe, nous analysons rapidement votre
                                situation juridique, extrayons les éléments pertinents de votre demande et vous
                                fournissons des informations précises et adaptées.</p>

                            <p>Notre système continue d'apprendre et de s'améliorer, garantissant des réponses toujours
                                plus pertinentes et des orientations juridiques de qualité.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="p-4 hover:bg-gray-100 transition-colors duration-200 rounded-b-lg">
                        <button class="flex justify-between items-center w-full text-left" onclick="toggleFaq('faq3')">
                            <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                                <i class="fas fa-ad mr-3 text-red-600"></i>Comment faire de la publicité sur le site ?
                            </h4>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform" id="faq3-icon"></i>
                        </button>
                        <div id="faq3" class="mt-3 text-gray-600 hidden">
                            <p>Si vous êtes un professionnel du droit et souhaitez mettre en avant vos services sur
                                notre plateforme, contactez notre équipe commerciale. Nous proposons différentes
                                formules de partenariat pour vous aider à toucher notre audience de personnes
                                intéressées par des services juridiques.</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        </div>
    </section>


    <!-- Questions Fréquentes Section -->
    <section class="py-16 bg-gradient-to-b from-white to-gray-100 relative overflow-hidden" data-aos="fade-up"
        data-aos-duration="1000">
        <!-- Decorative Elements -->
        <div
            class="absolute top-0 right-0 w-64 h-64 bg-red-600 rounded-full opacity-5 translate-x-1/3 -translate-y-1/2">
        </div>
        <div
            class="absolute bottom-0 left-0 w-96 h-96 bg-blue-700 rounded-full opacity-5 -translate-x-1/3 translate-y-1/3">
        </div>

        <div class="max-w-7xl mx-auto px-8 md:px-12 relative z-10">
            <!-- Section Header with Icon -->
            <div class="text-center mb-12" data-aos="fade-down" data-aos-duration="800">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Les Questions les plus fréquentes</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Découvrez les questions juridiques les plus courantes posées
                    par nos utilisateurs</p>
            </div>

            <!-- Questions Grid with Multiple Colors -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                <!-- Question Card 1 - Red -->
                <a href="#"
                    class="bg-red-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="100" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-gavel text-red-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Comment contester une amende?</span>
                </a>

                <!-- Question Card 2 - Blue -->
                <a href="#"
                    class="bg-blue-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="150" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-heart-broken text-blue-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Vos droits en cas de divorce</span>
                </a>

                <!-- Question Card 3 - Green -->
                <a href="#"
                    class="bg-green-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="200" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-balance-scale text-green-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Comment déclencher une procédure légale?</span>
                </a>

                <!-- Question Card 4 - Purple -->
                <a href="#"
                    class="bg-purple-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="250" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-briefcase text-purple-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Vos droits en cas de licenciement</span>
                </a>

                <!-- Question Card 5 - Indigo -->
                <a href="#"
                    class="bg-indigo-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="300" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-building text-indigo-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Comment créer une entreprise?</span>
                </a>

                <!-- Question Card 6 - Yellow -->
                <a href="#"
                    class="bg-yellow-500 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="350" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-scroll text-yellow-500 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Démarches pour déclarer une succession</span>
                </a>

                <!-- Question Card 7 - Teal -->
                <a href="#"
                    class="bg-teal-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="400" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-file-invoice-dollar text-teal-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Obligations fiscales des indépendants</span>
                </a>

                <!-- Question Card 8 - Orange -->
                <a href="#"
                    class="bg-orange-500 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="450" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-home text-orange-500 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Vos droits en tant que locataire</span>
                </a>

                <!-- Question Card 9 - Pink -->
                <a href="#"
                    class="bg-pink-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="500" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-file-signature text-pink-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Comment rédiger un contrat de travail?</span>
                </a>

                <!-- Question Card 10 - Cyan -->
                <a href="#"
                    class="bg-cyan-600 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="550" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-passport text-cyan-600 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Démarches pour obtenir une carte de séjour</span>
                </a>

                <!-- Question Card 11 - Amber -->
                <a href="#"
                    class="bg-amber-500 p-5 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 flex items-center"
                    data-aos="zoom-in" data-aos-delay="600" data-aos-duration="800">
                    <div
                        class="w-12 h-12 rounded-full bg-white flex items-center justify-center flex-shrink-0 mr-4 shadow-md">
                        <i class="fas fa-exclamation-triangle text-amber-500 text-lg"></i>
                    </div>
                    <span class="text-white font-medium">Conséquences d'un contrat non respecté</span>
                </a>

                <!-- View All Button -->
                <div class="lg:col-span-3 text-center mt-8" data-aos="fade-up" data-aos-delay="700"
                    data-aos-duration="800">
                    <a href="#"
                        class="inline-flex items-center px-8 py-3 bg-red-600 text-white font-medium rounded-full hover:bg-red-700 transition-all duration-300 shadow-md transform hover:scale-105">
                        <i class="fas fa-search mr-2"></i>
                        <span>Voir toutes les questions fréquentes</span>
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>


    <!-- Références Officielles Section -->
    <section id="references" class="py-16 bg-gradient-to-b from-gray-50 to-white" data-aos="fade-up">
        <div class="max-w-7xl mx-auto px-8 md:px-12">
            <!-- Section Header -->
            <div class="text-center mb-12">

                <h2 class="text-3xl font-bold text-gray-800 mb-2">Références Officielles</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Les sites gouvernementaux et officiels pour vos démarches
                    juridiques</p>
            </div>

            <!-- Main Feature: Justice.fr -->
            <div class="relative overflow-hidden bg-white rounded-2xl shadow-xl mb-16" data-aos="fade-up">

                <div class="md:flex relative z-10">
                    <!-- Left: Image with overlay -->
                    <div class="md:w-2/5 relative">
                        <div class="h-full min-h-[300px]">
                            <img src="img/photo/court1.jpg" alt="Justice.fr" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/60 to-transparent"></div>
                        </div>
                        <div class="absolute bottom-0 left-0 p-6">
                            <span
                                class="bg-blue-700 text-white px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
                                <i class="fas fa-star mr-2"></i>Site Officiel
                            </span>
                        </div>
                    </div>

                    <!-- Right: Content -->
                    <div class="md:w-3/5 p-8 md:p-10">
                        <div class="flex items-center mb-4">
                            <h2 class="text-3xl font-bold text-gray-800">Justice.fr</h2>
                            <span
                                class="ml-3 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-semibold">Gouvernemental</span>
                        </div>

                        <p class="text-gray-700 mb-6 leading-relaxed">
                            Le point d'entrée numérique essentiel pour toute personne cherchant à effectuer des
                            démarches juridiques en France.
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-2 mr-3 flex-shrink-0">
                                    <i class="fas fa-file-alt text-blue-700"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Démarches en ligne</h4>
                                    <p class="text-sm text-gray-600">Effectuez vos démarches auprès des tribunaux</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-2 mr-3 flex-shrink-0">
                                    <i class="fas fa-balance-scale text-blue-700"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Droits et obligations</h4>
                                    <p class="text-sm text-gray-600">Consultez des informations sur vos droits</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-2 mr-3 flex-shrink-0">
                                    <i class="fas fa-search-location text-blue-700"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Annuaire judiciaire</h4>
                                    <p class="text-sm text-gray-600">Trouvez un tribunal ou un professionnel</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="bg-blue-100 rounded-full p-2 mr-3 flex-shrink-0">
                                    <i class="fas fa-clipboard-list text-blue-700"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Suivi d'affaires</h4>
                                    <p class="text-sm text-gray-600">Suivez l'avancement de vos procédures</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <a href="http://www.justice.fr" target="_blank"
                                class="inline-flex items-center bg-blue-700 text-white px-6 py-3 rounded-full hover:bg-blue-800 transition duration-300 shadow-md">
                                <span>Consulter Justice.fr</span>
                                <i class="fas fa-external-link-alt ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Official Resources -->
            <h3 class="text-2xl font-bold text-gray-800 mb-6 pl-4 border-l-4 border-blue-700">Autres ressources
                officielles</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Card 1: Aide Juridictionnelle -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
                    data-aos="fade-up" data-aos-delay="100">
                    <div class="h-40 bg-gradient-to-r from-blue-700 to-blue-500 relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <img src="img/logo/aide-juridictionelle.png" alt="Aide Juridictionnelle"
                                class="h-24 object-contain">
                        </div>
                        <div
                            class="absolute top-4 right-4 bg-white/90 rounded-full px-3 py-1 text-xs font-semibold text-blue-700">
                            Officiel
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Aide Juridictionnelle</h3>
                        <p class="text-gray-600 mb-4">Demandez une aide financière pour la prise en charge de vos frais
                            de justice. Service sécurisé et simplifié pour faciliter vos démarches.</p>

                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">justice.fr</span>
                            <a href="https://www.aidejuridictionnelle.justice.fr/" target="_blank"
                                class="inline-flex items-center text-blue-700 hover:text-blue-800 font-semibold">
                                <span>Consulter</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Card 2: Service Public -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
                    data-aos="fade-up" data-aos-delay="200">
                    <div class="h-40 bg-gradient-to-r from-blue-600 to-indigo-600 relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <img src="img/logo/service-public.png" alt="Service Public" class="h-24 object-contain">
                        </div>
                        <div
                            class="absolute top-4 right-4 bg-white/90 rounded-full px-3 py-1 text-xs font-semibold text-blue-700">
                            Officiel
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Consultation Légale Gratuite</h3>
                        <p class="text-gray-600 mb-4">Découvrez comment accéder à des consultations gratuites avec un
                            avocat. Service-Public.fr détaille les différentes options disponibles.</p>

                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">service-public.fr</span>
                            <a href="https://www.service-public.fr/particuliers/vosdroits/F20706" target="_blank"
                                class="inline-flex items-center text-blue-700 hover:text-blue-800 font-semibold">
                                <span>Consulter</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Card 3: Legifrance -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
                    data-aos="fade-up" data-aos-delay="300">
                    <div class="h-40 relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <img src="img/logo/logoLegifrance.svg" alt="Legifrance" class="h-24 object-contain">
                        </div>
                        <div
                            class="absolute top-4 right-4 bg-white/90 rounded-full px-3 py-1 text-xs font-semibold text-red-700">
                            Officiel
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Légifrance</h3>
                        <p class="text-gray-600 mb-4">La source officielle pour consulter la législation et la
                            réglementation en vigueur en France. Accédez à tous les textes juridiques.</p>

                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">legifrance.gouv.fr</span>
                            <a href="https://www.legifrance.gouv.fr/" target="_blank"
                                class="inline-flex items-center text-blue-700 hover:text-blue-800 font-semibold">
                                <span>Consulter</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Les professionnels du droits Section -->
    <section id="professionnels" class="py-16 bg-gradient-to-b from-white to-gray-100" data-aos="fade-up">
        <div class="max-w-7xl mx-auto px-8 md:px-12">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Professionnels du Droit</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Trouvez les experts juridiques dont vous avez besoin pour
                    vous accompagner dans vos démarches</p>
            </div>

            <!-- Avocats Section -->
            <div class="mb-16">
                <h3 class="text-2xl font-bold text-gray-800 mb-6 pl-4 border-l-4 border-blue-700">Les Avocats</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Card 1: Trouver un avocat -->
                         <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="100">
                            <div class="h-3 bg-blue-700"></div>
                            <div class="relative h-48 overflow-hidden">
                                <img src="img/photo/groupeavocat1.jpeg" alt="Les Avocats"
                                    class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <div
                                        class="bg-blue-700 text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md inline-block mb-2">
                                        Géolocalisation
                                    </div>
                                    <h3 class="text-xl font-bold">Trouver un avocat à proximité</h3>
                                </div>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-600 mb-4">Localisez les cabinets d'avocats les plus proches de chez
                                    vous pour obtenir une consultation juridique personnalisée.</p>
                                <a href="#" target="_blank" id="LinkAvocatGoogleMap">
                                    <div class="flex justify-center">
                                    <span
                                        class="flex group relative w-full bg-white text-green-600 border border-green-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 hover:bg-green-600 hover:text-white justify-center items-center">
                                        <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="ml-2">Avec Google Map</span>
                                    </span>
                                </div></a>
                            </div>
                            <div class="p-6 pb-2">
                                <a href="#" target="_blank" id="LinkAvocatPageJaune">
                                   <div class="flex justify-center">
                                    <span
                                        class="flex group relative w-full bg-white text-yellow-600 border border-yellow-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 hover:bg-yellow-600 hover:text-white justify-center items-center"
                                        >
                                        <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="ml-2">Avec Pages Jaunes</span>
                                    </span>
                                </div>
                                </a>
                           </div>
                       </div>
                   

                    <!-- Card 2: Avocat.fr -->
                    <a href="https://www.avocats.fr/" target="_blank" class="block group">
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="200">
                            <div class="h-3 bg-blue-700"></div>
                            <div class="relative h-48 overflow-hidden bg-blue-50 flex items-center justify-center">
                                <img src="img/logo/logoavocatfr.svg" alt="Avocat.fr"
                                    class="h-24 transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute top-4 right-4">
                                    <div
                                        class="bg-blue-700 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                                        Officiel
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div
                                        class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-blue-200">
                                        <i class="fas fa-gavel text-blue-700 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3
                                            class="text-xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors duration-300">
                                            Avocat.fr: La Référence</h3>
                                        <p class="text-sm text-gray-500">Portail officiel</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6">Le portail officiel du Conseil National des Barreaux
                                    (CNB), l'institution représentant tous les avocats en France.</p>
                                <div
                                    class="flex items-center text-blue-700 font-medium group-hover:text-blue-800 transition-colors duration-300">
                                    <span>Visiter le site</span>
                                    <i
                                        class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                                </div>
                            </div>
                        </div>
                    </a>

                    <!-- Card 3: Alexia.fr -->
                    <a href="https://www.alexia.fr/" target="_blank" class="block group">
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="300">
                            <div class="h-3 bg-purple-600"></div>
                            <div class="relative h-48 overflow-hidden bg-gray-50 flex items-center justify-center">
                                <img src="img/logo/alexiafr.png" alt="Alexia.fr"
                                    class="h-24 transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute top-4 right-4">
                                    <div
                                        class="bg-purple-600 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                                        Plateforme
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div
                                        class="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-purple-200">
                                        <i class="fas fa-search text-purple-600 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3
                                            class="text-xl font-bold text-gray-800 group-hover:text-purple-700 transition-colors duration-300">
                                            Alexia.fr</h3>
                                        <p class="text-sm text-gray-500">Recherche d'avocats</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6">Plateforme qui simplifie la recherche d'avocats selon
                                    leurs spécialités et leur localisation pour répondre à vos besoins juridiques.</p>
                                <div
                                    class="flex items-center text-purple-600 font-medium group-hover:text-purple-800 transition-colors duration-300">
                                    <span>Visiter le site</span>
                                    <i
                                        class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Notaires et Huissiers Section -->
            <div>
                <h3 class="text-2xl font-bold text-gray-800 mb-6 pl-4 border-l-4 border-blue-700">Les Notaires et
                    Huissiers</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Card 1: Trouver un notaire -->
                          <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="100">
                            <div class="h-3 bg-green-700"></div>
                            <div class="relative h-48 overflow-hidden">
                                <img src="img/photo/bienvenuenotaire.jpg" alt="Les Notaires"
                                    class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <div
                                        class="bg-green-700 text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md inline-block mb-2">
                                        Géolocalisation
                                    </div>
                                    <h3 class="text-xl font-bold">Trouver un notaire à proximité</h3>
                                </div>
                            </div>
                            <div class="p-6">
                                <p class="text-gray-600 mb-4">Localisez les études notariales les plus proches de chez vous pour vos actes authentiques et conseils juridiques.
</p>
                                <a href="#" target="_blank" id="LinkNotaireGoogleMap">
                                    <div class="flex justify-center">
                                    <span
                                        class="flex group relative w-full bg-white text-green-600 border border-green-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 hover:bg-green-600 hover:text-white justify-center items-center">
                                        <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="ml-2">Avec Google Map</span>
                                    </span>
                                </div></a>
                            </div>
                            <div class="p-6 pb-2">
                                <a href="#" target="_blank" id="LinkNotairePageJaune">
                                   <div class="flex justify-center">
                                    <span
                                        class="flex group relative w-full bg-white text-yellow-600 border border-yellow-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 hover:bg-yellow-600 hover:text-white justify-center items-center"
                                        >
                                        <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="ml-2">Avec Pages Jaunes</span>
                                    </span>
                                </div>
                                </a>
                           </div>
                       </div>
                    

                    <!-- Card 2: Notaires.fr -->
                    <a href="https://www.notaires.fr" target="_blank" class="block group">
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="200">
                            <div class="h-3 bg-green-600"></div>
                            <div class="relative h-48 overflow-hidden bg-green-50 flex items-center justify-center">
                                <img src="img/logo/notairesfr.svg" alt="Notaires.fr"
                                    class="h-24 transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute top-4 right-4">
                                    <div
                                        class="bg-green-600 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                                        Officiel
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div
                                        class="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-green-200">
                                        <i class="fas fa-file-signature text-green-600 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3
                                            class="text-xl font-bold text-gray-800 group-hover:text-green-700 transition-colors duration-300">
                                            Notaires.fr</h3>
                                        <p class="text-sm text-gray-500">Site officiel</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6">Le site officiel des Notaires de France offrant un large
                                    éventail d'informations et de services liés au notariat.</p>
                                <div
                                    class="flex items-center text-green-600 font-medium group-hover:text-green-800 transition-colors duration-300">
                                    <span>Visiter le site</span>
                                    <i
                                        class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                                </div>
                            </div>
                        </div>
                    </a>

                    <!-- Card 3: Devis-Huissier.fr -->
                    <a href="https://www.devis-huissier.fr" target="_blank" class="block group">
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                            data-aos="zoom-in" data-aos-delay="300">
                            <div class="h-3 bg-amber-500"></div>
                            <div class="relative h-48 overflow-hidden bg-gray-50 flex items-center justify-center">
                                <img src="img/logo/logo-devis-huissier.png" alt="Devis-Huissier.fr"
                                    class="h-24 transform transition-transform duration-700 group-hover:scale-110">
                                <div class="absolute top-4 right-4">
                                    <div
                                        class="bg-amber-500 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                                        Service
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    <div
                                        class="w-14 h-14 bg-amber-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-amber-200">
                                        <i class="fas fa-file-invoice-dollar text-amber-500 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3
                                            class="text-xl font-bold text-gray-800 group-hover:text-amber-600 transition-colors duration-300">
                                            Devis-Huissiers.fr</h3>
                                        <p class="text-sm text-gray-500">Service de devis</p>
                                    </div>
                                </div>
                                <p class="text-gray-600 mb-6">Plateforme permettant de trouver des huissiers de justice
                                    situés à proximité et d'obtenir des devis pour vos procédures.</p>
                                <div
                                    class="flex items-center text-amber-500 font-medium group-hover:text-amber-600 transition-colors duration-300">
                                    <span>Visiter le site</span>
                                    <i
                                        class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Sites Utiles Section -->
    <section id="utiles" class="py-16 bg-gradient-to-b from-white to-gray-100" data-aos="fade-up">
        <div class="max-w-7xl mx-auto px-8 md:px-12">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Ressources Juridiques en Ligne</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Une sélection de sites web utiles pour compléter vos
                    recherches juridiques</p>
            </div>

            <!-- Sites Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Site Card 1 - Dalloz -->
                <a href="https://www.dalloz.fr/" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="100">
                        <div class="h-3 bg-purple-600"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-purple-200">
                                    <i class="fas fa-book text-purple-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-purple-700 transition-colors duration-300">
                                        Dalloz.fr</h3>
                                    <p class="text-sm text-gray-500">Références juridiques</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Base de données complète de jurisprudence, doctrine et textes
                                législatifs. Ressource incontournable pour les professionnels du droit.</p>

                            <div
                                class="inline-flex items-center text-purple-600 font-medium group-hover:text-purple-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Site Card 2 - Droit.org -->
                <a href="https://www.droit.org/" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="200">
                        <div class="h-3 bg-blue-600"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-blue-200">
                                    <i class="fas fa-balance-scale text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors duration-300">
                                        Droit.org</h3>
                                    <p class="text-sm text-gray-500">Références juridiques</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Portail du droit français en ligne offrant un accès libre aux
                                textes de loi, à la jurisprudence et aux ressources juridiques.</p>

                            <div
                                class="inline-flex items-center text-blue-600 font-medium group-hover:text-blue-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Site Card 3 - Juripredis -->
                <a href="https://juripredis.com" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="300">
                        <div class="h-3 bg-green-600"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-green-200">
                                    <i class="fas fa-comments text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-green-700 transition-colors duration-300">
                                        Juripredis.com</h3>
                                    <p class="text-sm text-gray-500">Information juridique</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Plateforme d'information juridique proposant des articles,
                                guides et conseils pratiques sur divers aspects du droit français.</p>

                            <div
                                class="inline-flex items-center text-green-600 font-medium group-hover:text-green-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Site Card 4 - Litige.fr -->
                <a href="http://www.Litige.fr" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="400">
                        <div class="h-3 bg-red-600"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-red-200">
                                    <i class="fas fa-gavel text-red-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-red-700 transition-colors duration-300">
                                        Litige.fr</h3>
                                    <p class="text-sm text-gray-500">Règlement de litiges</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Service en ligne dédié à la résolution des litiges, offrant
                                des conseils et des outils pour régler les différends juridiques.</p>

                            <div
                                class="inline-flex items-center text-red-600 font-medium group-hover:text-red-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Site Card 5 - Forum juridique -->
                <a href="https://www.forum-juridique.net/" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="500">
                        <div class="h-3 bg-yellow-500"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-yellow-200">
                                    <i class="fas fa-users text-yellow-500 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-yellow-700 transition-colors duration-300">
                                        Forum-juridique.net</h3>
                                    <p class="text-sm text-gray-500">Forum d'entraide</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Communauté en ligne où les utilisateurs peuvent poser des
                                questions juridiques et recevoir des réponses de la communauté.</p>

                            <div
                                class="inline-flex items-center text-yellow-600 font-medium group-hover:text-yellow-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <!-- Site Card 6 - Justifit -->
                <a href="https://www.justifit.fr" target="_blank" class="group">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
                        data-aos="zoom-in" data-aos-delay="600">
                        <div class="h-3 bg-indigo-600"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div
                                    class="w-14 h-14 bg-indigo-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-indigo-200">
                                    <i class="fas fa-user-tie text-indigo-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3
                                        class="text-xl font-bold text-gray-800 group-hover:text-indigo-700 transition-colors duration-300">
                                        Justifit.fr</h3>
                                    <p class="text-sm text-gray-500">Trouver un avocat</p>
                                </div>
                            </div>

                            <p class="text-gray-600 mb-6">Plateforme de mise en relation avec des avocats qualifiés
                                selon votre besoin juridique et votre localisation.</p>

                            <div
                                class="inline-flex items-center text-indigo-600 font-medium group-hover:text-indigo-800 transition-colors duration-300">
                                <span>Consulter</span>
                                <i
                                    class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </section>
    </div>
    </section>


    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="Articles/"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="#comment"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Comment ça marche?
                            </a>
                        </li>
                        <li>
                            <a href="#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                         <li>
                            <a href="mentions.html#politique"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                               Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="mentions.html#conditions"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="services.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="services.html#promotion"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|</span>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>

    <script>

        //Localisation google
        function LocAvocatG() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');


            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;


                    alert('function click2');
                    // Create Google Maps URL with current location
                    //const mapsUrl = `https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`;
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;
        }

        function LocAvocatPJ() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');

            window.open(`https://www.alexia.fr`, "_blank", "noopener,noreferrer");

            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;

                    //TODO Find city or zip with lat,long


                    // Create Google Maps URL with current location
                    //https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat&ou=75
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;

        }

        initStatic();


    </script>


</body>

</html>
