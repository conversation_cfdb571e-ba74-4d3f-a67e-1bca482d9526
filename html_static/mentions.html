<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestionLegale.info: Trouvez des réponses juridiques en France</title>
    <meta name="description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="justice France, démarches juridiques, tribunal, droits, procédures judiciaires,
         QuestionLegale, Reponses Juridiques, Avocats, Huissiers, Notaires, Questions Legales, Reponses Juridiques, Avocats, Huissiers, Notaires">

    <meta property="og:title" content="QuestionLegale.info: Trouvez des réponses juridiques en France">
    <meta property="og:description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta property="og:image" content="img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="img/favicons/rowserconfig.xml">
    <meta name="msapplication-TileImage" content="img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="img/favicons/logo.png">


    <style>
        /* Custom styles */


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        .presentation-card {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }


        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }
    </style>
</head>


<body class="bg-gray-300 text-gray-800">

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="index.html" tooltip="Question Legale">
            <div class="flex items-center space-x-2">

                <!-- Icon: replace emoji with SVG or image if needed -->
                <img src="img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
                <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>

            </div>
        </a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <button type="button" id="mobileSignIn"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button" id="mobileSignUp"
                class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap">
                Inscription
            </button>
            <!-- Burger menu button -->
            <button type="button" id="burgerBtn" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="index.html#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="Articles/index.htm" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="index.html#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <button type="button" id="signInBtn"
                class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button" id="signUpBtn"
                class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap">
                Inscription
            </button>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="index.html#analyse" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question?</a>
            <a href="index.html#references" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="index.html#professionnels" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Les Professionnels</a>
            <a href="index.html#utiles" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Sites Utiles</a>
            <a href="index.html#Comment ca marche?" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Comment ca marche?</a>
        </div>
        <!-- Buttons in mobile menu -->
        <div class="flex space-x-3 mt-4">
            <button type="button"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button"
                class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Inscription
            </button>
        </div>
    </div>

    <!-- Presentation -->
    <section id="home" class="relative mb-12 py-16">
        <!-- Background image with overlay -->
        <div class="absolute inset-0" data-aos="fade" data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-0"></div>
        </div>

        <div class="relative z-10 flex flex-col md:flex-row pt-8 md:pt-8 items-start">
            <!-- Contenu de la page -->
            <div class="w-full flex flex-col justify-start px-8 md:px-12 mt-4 md:mt-8" data-aos="fade-down"
                data-aos-duration="800">
                <div class="bg-white bg-opacity-80 rounded-xl shadow-xl p-8 md:p-12 my-8">
                    <h1
                        class="text-3xl font-extrabold sm:text-4xl text-black mb-4 pb-2 border-b border-gray-300 text-center">
                        Mentions légales</h1>

                    <p class="mb-4">Le présent site internet, "QuestionLegale.info", est une plateforme numérique dédiée
                        à l'information juridique et légale. Son objectif principal est de fournir aux utilisateurs un
                        accès simplifié à des contenus informatifs fiables, des conseils pratiques et des ressources
                        pertinentes sur une multitude d'aspects du droit français. Nous nous engageons à démocratiser la
                        connaissance juridique, en couvrant des thématiques aussi variées que le droit des affaires, le
                        droit du travail, le droit de la consommation, le droit de la famille, le droit immobilier, et
                        bien d'autres domaines essentiels à la vie quotidienne et professionnelle.</p>

                    <p class="mb-4">Tous les contenus publiés sur cette plateforme sont élaborés avec le plus grand
                        soin, dans un souci constant d'exactitude, de clarté et d'actualisation. Notre équipe s'efforce
                        de vérifier chaque information et de la maintenir à jour en fonction des évolutions
                        législatives, réglementaires et jurisprudentielles. Cependant, le droit est une matière complexe
                        et en constante mutation. Il est donc possible que, malgré notre vigilance, certaines données
                        deviennent obsolètes ou nécessitent des ajustements. C'est pourquoi nous encourageons vivement
                        nos utilisateurs à toujours consulter les sources officielles et, pour toute situation
                        spécifique ou en cas de doute, à solliciter l'avis d'un professionnel du droit qualifié (avocat,
                        notaire, huissier de justice) afin d'obtenir des conseils personnalisés et adaptés à leur
                        situation.</p>

                    <p class="mb-4">L'accès à "QuestionLegale.info" est entièrement gratuit et ouvert à tous, sous
                        réserve de disposer d'une connexion internet stable et de respecter les conditions générales
                        d'utilisation du site. L'utilisation de notre plateforme implique une acceptation pleine et
                        entière des présentes mentions légales. Nous nous réservons le droit de modifier ces mentions à
                        tout moment, sans préavis, afin de nous conformer aux évolutions légales, réglementaires ou
                        techniques. Il est de la responsabilité de l'utilisateur de consulter régulièrement cette page
                        pour prendre connaissance des éventuelles mises à jour.</p>

                    <p class="mb-4">L'ensemble des textes, images, documents, éléments graphiques, logos, icônes et tout
                        autre contenu présent sur ce site sont la propriété exclusive de "QuestionLegale.info" ou de ses
                        partenaires, et sont protégés par les lois en vigueur relatives à la propriété intellectuelle,
                        notamment le droit d'auteur. Toute reproduction, représentation, modification, publication,
                        adaptation, totale ou partielle, des éléments du site, quel que soit le moyen ou le procédé
                        utilisé, est strictement interdite sans l'autorisation écrite préalable de l'éditeur du site.
                        Toute exploitation non autorisée du site ou de l'un quelconque des éléments qu'il contient sera
                        considérée comme constitutive d'une contrefaçon et poursuivie conformément aux dispositions des
                        articles L.335-2 et suivants du Code de Propriété Intellectuelle.</p>

                    <p class="mb-4">Le site "QuestionLegale.info" peut contenir des liens hypertextes renvoyant vers des
                        sources externes, y compris des sites internet de partenaires, d'organismes officiels,
                        d'institutions juridiques ou d'autres ressources informatives. Ces liens sont fournis à titre
                        purement indicatif et informatif pour faciliter la recherche d'informations complémentaires par
                        l'utilisateur. L'éditeur du site ne saurait en aucun cas être tenu responsable des contenus, des
                        erreurs, des omissions, de la disponibilité ou des pratiques de confidentialité de ces
                        plateformes externes. La consultation de ces sites tiers se fait sous l'entière responsabilité
                        de l'utilisateur.</p>

                    <p class="mb-4">Enfin, il est impératif de souligner que les informations et ressources mises à
                        disposition sur "QuestionLegale.info" ont une vocation strictement informative et documentaire.
                        Elles ne sauraient en aucun cas être interprétées comme une consultation juridique
                        personnalisée, un avis professionnel ou un substitut à une assistance juridique qualifiée.
                        Chaque situation étant unique, l'utilisateur est seul responsable de l'utilisation qu'il fait
                        des données proposées sur le site. L'éditeur du site décline toute responsabilité quant aux
                        conséquences directes ou indirectes qui pourraient découler de l'utilisation des informations
                        contenues sur cette plateforme sans l'avis préalable d'un expert du droit.</p>

                    <h1 id="politique"
                        class="text-3xl font-extrabold sm:text-4xl text-black mb-4 mt-8 pb-2 border-b border-gray-300 text-center">
                        Politique de confidentialité</h1>

                    <p class="mb-4">La protection de vos données personnelles est une priorité absolue pour
                        "QuestionLegale.info". Cette politique de confidentialité détaillée a pour objectif de vous
                        informer de manière transparente sur les modalités de collecte, de traitement, de stockage et de
                        sécurisation des informations que vous nous confiez lorsque vous naviguez sur notre site
                        internet. Nous nous engageons à respecter votre vie privée et à traiter vos données conformément
                        aux réglementations en vigueur, notamment le Règlement Général sur la Protection des Données
                        (RGPD) de l'Union Européenne.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Collecte et traitement des données</h2>
                    <p class="mb-4">Lors de votre interaction avec notre site, diverses informations peuvent être
                        collectées. Cela se produit notamment lorsque vous remplissez un formulaire de contact, vous
                        abonnez à notre newsletter, participez à des sondages, ou interagissez avec certaines
                        fonctionnalités du site (par exemple, via des commentaires ou des requêtes spécifiques). Les
                        données collectées peuvent inclure, sans s'y limiter, votre nom, votre adresse e-mail, votre
                        numéro de téléphone, votre localisation géographique approximative (déduite de votre adresse
                        IP), ainsi que toute autre information que vous choisissez de nous fournir et qui est jugée
                        pertinente pour l'amélioration de nos services et la personnalisation de votre expérience
                        utilisateur. Ces données sont traitées de manière loyale et licite, et sont utilisées uniquement
                        aux fins pour lesquelles elles ont été collectées, telles que la fourniture d'informations, la
                        réponse à vos questions, l'envoi de communications ciblées (avec votre consentement), et
                        l'amélioration continue de la qualité de notre plateforme.</p>

                    <p class="mb-4">L’usage des cookies et autres technologies de suivi similaires nous permet également
                        de recueillir des données anonymes ou pseudonymisées liées à votre navigation sur le site. Ces
                        informations incluent, par exemple, les pages que vous consultez, le temps passé sur chaque
                        page, les liens sur lesquels vous cliquez, le type de navigateur et de système d'exploitation
                        que vous utilisez, et l'adresse IP de votre appareil. Ces données sont essentielles pour
                        l'analyse statistique de l'audience de notre site, l'optimisation de l'affichage des contenus,
                        la personnalisation de l'expérience utilisateur, et l'amélioration de la performance globale de
                        la plateforme. Les cookies ne nous permettent en aucun cas de vous identifier personnellement,
                        sauf si vous nous fournissez volontairement des informations nominatives.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Droits des utilisateurs</h2>
                    <p class="mb-4">Conformément aux dispositions du Règlement Général sur la Protection des Données
                        (RGPD) et à la loi "Informatique et Libertés", chaque utilisateur de "QuestionLegale.info"
                        dispose de droits fondamentaux concernant ses données personnelles. Vous avez le droit de
                        demander l'accès à vos données, de les faire rectifier si elles sont inexactes ou incomplètes,
                        de demander leur suppression (droit à l'oubli), de vous opposer à leur traitement, de demander
                        la limitation de leur traitement, et de bénéficier de la portabilité de vos données. Pour
                        exercer l'un de ces droits, ou pour toute question relative à la protection de vos données
                        personnelles, vous pouvez nous contacter via les moyens de communication mis à votre disposition
                        sur le site. Nous nous engageons à répondre à toutes les demandes légitimes dans les meilleurs
                        délais et conformément aux exigences légales.</p>

                    <p class="mb-4">Toutes demandes relatives à l’exercice de ces droits peuvent être adressées via les
                        moyens de contact mis à disposition sur le site. Nous nous engageons à traiter ces requêtes avec
                        diligence et à vous fournir une réponse dans les délais légaux.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Sécurisation des données</h2>
                    <p class="mb-4">Nous mettons en œuvre toutes les mesures techniques et organisationnelles
                        nécessaires pour garantir un niveau de sécurité optimal des données personnelles que nous
                        recueillons. Cela inclut des procédures rigoureuses de cryptage des données en transit et au
                        repos, des systèmes de gestion sécurisée des accès (authentification forte, gestion des
                        autorisations), des audits de sécurité réguliers, et une surveillance constante de nos
                        infrastructures pour prévenir toute compromission, perte, altération, divulgation non autorisée
                        ou accès illicite aux informations. Nous nous engageons à protéger vos données contre toute
                        utilisation abusive ou malveillante.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Cookies et suivi</h2>
                    <p class="mb-4">Les cookies utilisés sur "QuestionLegale.info" ont pour finalité principale
                        d’améliorer votre expérience de navigation en vous proposant des contenus et des fonctionnalités
                        adaptés à vos préférences et à votre comportement sur le site. Ces cookies peuvent être de
                        différentes natures : cookies de session (disparaissant dès la fermeture de votre navigateur),
                        cookies persistants (restant sur votre appareil pour une durée déterminée), cookies fonctionnels
                        (permettant de mémoriser vos choix), cookies de performance (pour analyser l'utilisation du
                        site), et cookies publicitaires (pour afficher des publicités ciblées). Vous avez la possibilité
                        de configurer les paramètres de votre navigateur internet afin d'accepter ou de refuser
                        l'installation de ces cookies, ou d'être informé de leur utilisation. Il est important de noter
                        que le refus de certains types de cookies peut affecter la fonctionnalité et l'expérience de
                        navigation sur notre site.</p>

                    <h1 id="conditions"
                        class="text-3xl font-extrabold sm:text-4xl text-black mb-4 mt-8 pb-2 border-b border-gray-300 text-center">
                        Conditions d’utilisation</h1>

                    <p class="mb-4">L’utilisation du site "QuestionLegale.info" est encadrée par des règles essentielles
                        visant à garantir une expérience de navigation fluide, sécurisée et respectueuse des droits de
                        chaque utilisateur. En accédant à notre plateforme, l'internaute reconnaît avoir pris pleinement
                        connaissance des présentes conditions générales d'utilisation et accepte de s'y conformer sans
                        réserve. Ces conditions constituent un contrat entre l'utilisateur et l'éditeur du site,
                        définissant les droits et obligations de chacun.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Objet du site et accès</h2>
                    <p class="mb-4">Le site "QuestionLegale.info" a pour vocation exclusive de fournir des informations
                        juridiques et légales sous diverses formes : articles de fond, guides pratiques, fiches
                        thématiques, et conseils généraux. Il est impératif de comprendre que le contenu de ce site ne
                        constitue en aucun cas une consultation juridique personnalisée, un avis professionnel ou un
                        substitut à l'intervention d'un avocat, d'un notaire, d'un huissier de justice ou de tout autre
                        professionnel du droit qualifié. Notre rôle est d'informer et d'orienter, non de conseiller sur
                        des cas spécifiques. L'accès au site est ouvert à toute personne disposant d'une connexion
                        internet. Cependant, nous nous réservons le droit de suspendre temporairement ou définitivement
                        l'accès à tout ou partie du site pour des raisons techniques, de maintenance, de sécurité, ou en
                        cas de non-respect des présentes conditions.</p>

                    <p class="mb-4">L’accès au site est ouvert à toute personne disposant d’une connexion internet. Il
                        peut cependant être temporairement suspendu pour des raisons techniques ou de maintenance.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Utilisation des contenus</h2>
                    <p class="mb-4">Tous les articles, documents, ressources et outils mis en ligne sur
                        "QuestionLegale.info" sont destinés à un usage strictement personnel, informatif et éducatif.
                        Toute exploitation commerciale, reproduction, diffusion, modification, ou utilisation non
                        autorisée des contenus, sous quelque forme que ce soit, est formellement interdite et pourra
                        faire l'objet de poursuites judiciaires. L'utilisateur s'engage à ne pas altérer le contenu du
                        site, ni à en faire un usage détourné à des fins malveillantes, frauduleuses, illégales ou
                        contraires aux bonnes mœurs. Toute infraction aux règles de propriété intellectuelle, toute
                        tentative d'intrusion ou d'atteinte au bon fonctionnement du site, ou toute utilisation abusive
                        des services pourra entraîner des mesures de restriction d'accès, voire des poursuites pénales.
                    </p>

                    <p class="mb-4">L’utilisateur s’engage à ne pas altérer le contenu du site, ni à en faire un usage
                        détourné à des fins malveillantes ou frauduleuses. Toute infraction aux règles de propriété
                        intellectuelle ou toute atteinte au bon fonctionnement du site pourra entraîner des mesures de
                        restriction d’accès.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Modifications des conditions</h2>
                    <p class="mb-4">Les présentes conditions générales d'utilisation sont susceptibles d'être ajustées
                        et mises à jour à tout moment en fonction des évolutions légales, réglementaires, techniques ou
                        éditoriales. Chaque modification prendra effet dès sa publication sur le site. Il incombe à
                        l'utilisateur de consulter régulièrement cette page afin de se tenir informé des dernières mises
                        à jour. En continuant à utiliser le site après la publication des modifications, l'utilisateur
                        est réputé avoir accepté les nouvelles conditions.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Responsabilité de l’utilisateur</h2>
                    <p class="mb-4">L’utilisateur est seul et unique responsable de l’usage qu’il fait des informations,
                        conseils et ressources fournies sur le site "QuestionLegale.info". Il est fortement recommandé
                        de vérifier la fiabilité, l'actualité et la pertinence des données avant de les utiliser à des
                        fins professionnelles, juridiques ou personnelles importantes. L'éditeur du site décline toute
                        responsabilité en cas de dommages directs ou indirects, matériels ou immatériels, qui pourraient
                        découler de l'utilisation inappropriée, erronée ou non vérifiée des contenus mis en ligne. Notre
                        plateforme n'est qu'un outil d'information et ne saurait engager notre responsabilité pour des
                        décisions prises sur la base de ces informations sans consultation d'un expert.</p>
                    <h2 class="text-xl font-bold text-black mb-2">Droit applicable</h2>
                    <p class="mb-4">Les présentes conditions générales d'utilisation sont régies et interprétées
                        conformément aux lois françaises applicables en matière de droit numérique, de protection des
                        données personnelles et de droit de la consommation. En cas de litige ou de désaccord relatif à
                        l'interprétation ou à l'exécution des présentes conditions, les parties concernées sont invitées
                        à rechercher une solution amiable avant d'engager toute action en justice. À défaut de
                        résolution amiable, les tribunaux français compétents seront seuls habilités à connaître du
                        litige.</p>
                </div>
            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="index.html#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="Articles/index.htm"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="index.html#comment"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Comment ça marche?
                            </a>
                        </li>
                        <li>
                            <a href="index.html#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="index.html#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                        <li>
                            <a href="mentions.html#politique"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="mentions.html#conditions"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="services.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="services.html#promotion"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact
                                (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="mentions.html" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|</span>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>

    <script>

        //Localisation google
        function LocAvocatG() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');


            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;


                    alert('function click2');
                    // Create Google Maps URL with current location
                    //const mapsUrl = `https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`;
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;
        }

        function LocAvocatPJ() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');

            window.open(`https://www.alexia.fr`, "_blank", "noopener,noreferrer");

            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;

                    //TODO Find city or zip with lat,long


                    // Create Google Maps URL with current location
                    //https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat&ou=75
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;

        }

        initStatic();


    </script>


</body>

</html>
