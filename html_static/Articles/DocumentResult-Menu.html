<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document with Transparent Top-Right Menu</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 text-gray-900">

  <!-- Side Menu in the top-right corner with transparency -->
  <div class="fixed top-6 right-6 bg-white bg-opacity-75 hover:bg-opacity-100 transition duration-300 p-6 rounded-lg shadow-lg z-10">
    <h3 class="text-xl font-semibold mb-4">Chapters</h3>
    <ul class="space-y-4">
      <li><a href="#chapter1" id="menu-chapter1" class="text-lg hover:text-blue-500 transition duration-300">Chapter 1: Introduction</a></li>
      <li><a href="#chapter2" id="menu-chapter2" class="text-lg hover:text-blue-500 transition duration-300">Chapter 2: Background</a></li>
      <li><a href="#chapter3" id="menu-chapter3" class="text-lg hover:text-blue-500 transition duration-300">Chapter 3: Methodology</a></li>
      <li><a href="#chapter4" id="menu-chapter4" class="text-lg hover:text-blue-500 transition duration-300">Chapter 4: Results</a></li>
      <li><a href="#chapter5" id="menu-chapter5" class="text-lg hover:text-blue-500 transition duration-300">Chapter 5: Conclusion</a></li>
    </ul>
  </div>

  <!-- Main Content aligned to the left with reduced width -->
  <div class="max-w-screen-md px-8 pr-16 mx-auto">

    <!-- Chapter 1 -->
    <div id="chapter1" class="section py-12">
      <h2 class="text-3xl font-bold mb-4">Chapter 1: Introduction</h2>
      <p class="text-lg mb-6">This is the introduction section of the document. It provides an overview of the topic. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin volutpat nulla ut eros dictum, ac dignissim odio sollicitudin. Nulla nec posuere odio. Ut auctor, eros ac cursus dictum, ante purus fermentum dui, a cursus ligula neque ac odio.</p>
      <p class="text-lg mb-6">Vestibulum sit amet justo vitae mi ullamcorper dapibus. Morbi fermentum eu orci at malesuada. Nam interdum justo nisi, ac varius turpis blandit at. Aenean dapibus mauris id felis cursus, ut egestas lectus vulputate. Integer viverra turpis sit amet efficitur volutpat. Curabitur euismod purus nec enim luctus, at volutpat ex fermentum.</p>
      <p class="text-lg mb-6">Sed at malesuada erat. Sed a condimentum libero. Curabitur a sapien scelerisque, dignissim ipsum vel, congue sapien. Integer tempus, risus at tincidunt aliquam, mi ligula scelerisque lorem, at ullamcorper lorem arcu nec mi. Sed vulputate tristique eros, in pretium purus malesuada eu. Phasellus vitae sagittis nunc.</p>
      <p class="text-lg">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean nec lectus nec arcu feugiat pretium. Etiam vitae quam ligula. Nulla at gravida urna. Integer malesuada, nulla ac blandit egestas, eros orci feugiat nunc, vel luctus risus justo vel nisi. Phasellus sed arcu vitae purus vehicula volutpat. Morbi ultricies ante augue, ut rutrum magna euismod ut.</p>
    </div>

    <!-- Chapter 2 -->
    <div id="chapter2" class="section py-12">
      <h2 class="text-3xl font-bold mb-4">Chapter 2: Background</h2>
      <p class="text-lg mb-6">The background section provides more details about the history and context of the topic. Sed tristique dui sit amet velit tincidunt, sit amet consequat justo malesuada. Quisque venenatis, mi in vehicula consectetur, sapien risus fermentum lorem, at condimentum arcu turpis ac leo.</p>
      <p class="text-lg mb-6">Mauris ullamcorper, odio ut porttitor tempus, turpis ligula tristique ante, nec tempor ante turpis in nulla. Fusce cursus, nulla vel fermentum laoreet, risus elit tincidunt felis, in viverra turpis elit ut libero. Integer a velit nisl. Donec efficitur lorem eget erat maximus, at dapibus risus auctor. Nullam in ipsum turpis. Donec tempus dolor et felis placerat varius.</p>
      <p class="text-lg mb-6">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus a nunc et nunc ullamcorper sodales. Phasellus facilisis orci a lectus dignissim, sit amet tincidunt purus venenatis. Sed rhoncus augue sit amet leo lacinia, et ultricies magna venenatis. Integer non enim quis libero suscipit lacinia.</p>
      <p class="text-lg">Quisque vitae urna a enim fringilla pretium. Nunc eu diam urna. Integer vel sagittis velit, non rutrum metus. Donec ornare orci et orci malesuada, id tempor sapien convallis. Pellentesque suscipit orci at turpis auctor, eget auctor odio pretium. Nam tincidunt quam eu risus dictum, ut egestas metus efficitur.</p>
    </div>

    <!-- Chapter 3 -->
    <div id="chapter3" class="section py-12">
      <h2 class="text-3xl font-bold mb-4">Chapter 3: Methodology</h2>
      <p class="text-lg mb-6">The methodology section explains the methods and techniques used in the research. Fusce auctor tristique erat et scelerisque. Cras a ante nec eros volutpat sollicitudin. Curabitur sodales magna dui, et dictum orci euismod at. Proin in tortor dolor. Nulla a bibendum enim. Phasellus et convallis libero, id congue est. Donec ut dui ac nisl tempor iaculis et ac nunc.</p>
      <p class="text-lg mb-6">Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Sed tempor lacus vitae facilisis tincidunt. Integer rutrum, eros ut laoreet vulputate, justo felis convallis sem, vitae posuere elit felis sed nunc. Curabitur feugiat vehicula dui, at fermentum sem ullamcorper ut. Vivamus sit amet velit risus. Nulla fermentum quam nec risus hendrerit, sit amet suscipit ipsum fermentum.</p>
      <p class="text-lg mb-6">Aliquam erat volutpat. Proin vel nulla a velit laoreet suscipit a ut augue. Vivamus vitae sapien sit amet enim eleifend malesuada non eget nulla. Aenean ut tincidunt ligula. Vivamus vitae ante in felis maximus laoreet non id turpis. Aliquam id vehicula odio. Nullam at tincidunt sapien. Integer ac tempor velit, id pretium eros.</p>
      <p class="text-lg">Sed tempus auctor arcu a viverra. Aliquam euismod ante vel tortor aliquet, et elementum erat malesuada. Curabitur quis risus eget lorem placerat tincidunt. Nulla ultricies massa sit amet risus congue, sed sodales turpis lacinia. Ut suscipit, nulla a luctus gravida, purus ipsum auctor lectus, non malesuada velit risus ut nisl.</p>
    </div>

    <!-- Chapter 4 -->
    <div id="chapter4" class="section py-12">
      <h2 class="text-3xl font-bold mb-4">Chapter 4: Results</h2>
      <p class="text-lg mb-6">The results section presents the findings of the study or analysis. Nulla vel libero id velit pretium tincidunt. Aenean eget neque ac lorem volutpat fermentum. Nunc et risus non lectus varius ultricies. Duis gravida nisl eu maximus tempor. Donec eu mauris malesuada, suscipit odio vitae, sodales lorem.</p>
      <p class="text-lg mb-6">Cras at ante nec lorem tempor efficitur. Nam nec eros gravida, gravida nunc sed, dignissim augue. Integer aliquam erat eu suscipit tincidunt. Mauris id augue purus. Integer vulputate gravida diam, id dignissim turpis iaculis in. Nam suscipit libero eu sapien facilisis scelerisque.</p>
      <p class="text-lg mb-6">Cras nec risus quis lectus viverra iaculis a sit amet libero. Sed sagittis diam ante, et cursus lorem viverra at. Suspendisse vel risus nec urna tincidunt posuere sit amet et mauris. Aliquam erat volutpat. Curabitur cursus orci id sem aliquet, non hendrerit sem auctor.</p>
      <p class="text-lg">Donec ultrices libero ut nulla sodales, ac vulputate felis tempus. Nunc nec odio nec neque fermentum suscipit eu eget risus. Morbi malesuada turpis nec lorem gravida, id eleifend eros pellentesque. Phasellus feugiat dolor ut neque dictum maximus.</p>
    </div>

    <!-- Chapter 5 -->
    <div id="chapter5" class="section py-12">
      <h2 class="text-3xl font-bold mb-4">Chapter 5: Conclusion</h2>
      <p class="text-lg mb-6">The conclusion section wraps up the document. Curabitur ut odio sapien. Fusce sed nisi eget dolor suscipit laoreet a vel arcu. Proin tincidunt felis sed orci fermentum, id iaculis lorem porttitor. Vivamus mollis libero non suscipit cursus. Nulla facilisi.</p>
      <p class="text-lg mb-6">Morbi id elit et velit facilisis aliquam. Sed ac ligula vulputate, posuere eros ac, eleifend arcu. Nunc non auctor libero. Aenean id suscipit nulla. Integer dictum fringilla enim at hendrerit. Integer aliquam elit eget magna tempor, ac tempus elit posuere.</p>
      <p class="text-lg">Sed in malesuada mi. Curabitur aliquam nisi purus, ac tincidunt felis congue et. Ut id sem euismod, fringilla orci a, volutpat libero. Nulla consectetur libero ac nunc tempus, at vestibulum sem vehicula.</p>
    </div>

  </div>

  <script>
    // Smooth scrolling for chapter links
    document.querySelectorAll('.side-menu a').forEach(link => {
      link.addEventListener('click', function(event) {
        event.preventDefault();
        const targetId = this.getAttribute('href').substring(1);
        const targetSection = document.getElementById(targetId);
        window.scrollTo({
          top: targetSection.offsetTop - 50, // Offset for top spacing
          behavior: 'smooth'
        });
      });
    });

    // Highlight active menu link based on scroll position
    const sections = document.querySelectorAll('.section');
    const menuLinks = document.querySelectorAll('.side-menu a');

    // Function to highlight the active section
    function highlightActiveSection() {
      let currentSectionId = '';
      
      sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
          currentSectionId = section.id;
        }
      });

      // Highlight the corresponding menu link
      menuLinks.forEach(link => {
        if (link.getAttribute('href').substring(1) === currentSectionId) {
          link.classList.add('text-blue-500', 'font-semibold');
          link.classList.remove('text-gray-800');
        } else {
          link.classList.remove('text-blue-500', 'font-semibold');
          link.classList.add('text-gray-800');
        }
      });
    }

    // Listen for scroll events to update active section
    window.addEventListener('scroll', highlightActiveSection);

    // Run on page load to highlight the initial section in view
    highlightActiveSection();
  </script>

</body>
</html>
