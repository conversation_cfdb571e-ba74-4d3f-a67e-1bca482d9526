<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="793" height="658" viewBox="0 0 793 658" style="fill:none;stroke:none;fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><style class="text-font-style fontImports" data-font-family="Roboto">@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&amp;display=block');</style><g id="items" style="isolation: isolate"><g id="blend" style="mix-blend-mode: normal"><g id="g-root-id-0_cu_sy_v9v3qkws8bgr-fill" data-item-order="-129086" transform="translate(261.00250244140625, 127.998046875)"><g id="id-0_cu_sy_v9v3qkws8bgr-fill" stroke="none" fill="url(#gradient-cbcbcb-248399021)"><g><path d="M 97.0977 245.433L 129 213.53L 161.337 245.867C 212.414 232.087 250 185.433 250 130C 250 117.695 248.148 105.8231 244.708 94.647L 210 85.3476L 219.465 50.0235C 197.492 25.4599 165.551 10 130 10C 94.1819 10 62.0291 25.6927 40.0418 50.5779L 49.3565 85.3524L 15.3498 94.4612C 11.8726 105.6915 10 117.627 10 130C 10 184.865 46.8199 231.13 97.0977 245.433Z"></path></g></g></g><g id="g-root-id-3_cu_sy_4nkk30ws8bgs-fill" data-item-order="-129082" transform="translate(193.56597900390625, 121.3961181640625)"><g id="id-3_cu_sy_4nkk30ws8bgs-fill" stroke="none" fill="url(#gradient-4dd3ff-378786070)"><g><path d="M 104.373597 45.58892C 101.195221 33.722816 93.490113 23.069221 82.011506 16.440818C 59.054505 3.184021 29.69751 11.047622 16.440812 34.004524C 3.184021 56.961509 11.047623 86.318519 34.004517 99.575218C 45.483002 106.203621 58.561524 107.551918 70.427521 104.373512L 116.793 91.954414L 104.376404 45.599617C 104.375519 45.596016 104.374512 45.592521 104.373597 45.58892Z"></path></g></g></g><g id="g-root-id-2_cu_sy_18mkka4ws8c1x-fill" data-item-order="-129078" transform="translate(332.003173828125, 331.5283203125)"><g id="id-2_cu_sy_18mkka4ws8c1x-fill" stroke="none" fill="url(#gradient-bceb57-497517668)"><g><path d="M 91.940796 43.941101C 100.627289 52.62741 106 64.627594 106 77.882507C 106 104.392211 84.509705 125.882995 58 125.882995C 31.490296 125.882995 10 104.392211 10 77.882507C 10 64.627685 15.372589 52.627716 24.058899 43.941406L 57.999695 10L 91.933106 43.933288C 91.935608 43.935913 91.938202 43.938507 91.940796 43.941101Z"></path></g></g></g><g id="g-root-id-1_cu_sy_1qf3iy4ws8buu-fill" data-item-order="-129074" transform="translate(461.0028076171875, 121.3961181640625)"><defs><linearGradient id="gradient-cbcbcb-248399021" x2="0" y2="1"><stop offset="0" stop-color="#d9d9d9"></stop><stop offset="1" stop-color="#bcbcbc"></stop></linearGradient><linearGradient id="gradient-4dd3ff-378786070" x2="0" y2="1"><stop offset="0" stop-color="#7fdfff"></stop><stop offset="1" stop-color="#1ac6ff"></stop></linearGradient><linearGradient id="gradient-bceb57-497517668" x2="0" y2="1"><stop offset="0" stop-color="#cff976"></stop><stop offset="1" stop-color="#a8dd38"></stop></linearGradient><linearGradient id="gradient-64edab-240055791" x2="0" y2="1"><stop offset="0" stop-color="#83fac1"></stop><stop offset="1" stop-color="#44e095"></stop></linearGradient></defs><g id="id-1_cu_sy_1qf3iy4ws8buu-fill" stroke="none" fill="url(#gradient-64edab-240055791)"><g><path d="M 22.423309 45.585144C 25.602692 33.719238 33.308716 23.066345 44.787903 16.438842C 67.745911 3.184043 97.102173 11.049942 110.356995 34.008041C 123.612 56.966033 115.745972 86.322433 92.787781 99.57724C 81.308777 106.204635 68.230225 107.551834 56.364502 104.372436L 10 91.949539L 22.420502 45.595733C 22.421387 45.592239 22.422394 45.588638 22.423309 45.585144Z"></path></g></g></g><g id="g-root-tx_obligati_zitdwsws9qfb-fill" data-item-order="0" transform="translate(93.0028076171875, 37.998046875)"><g id="tx_obligati_zitdwsws9qfb-fill" stroke="none" fill="#484848"><g><text style="font: bold 20px Roboto, sans-serif; white-space: pre;" font-weight="bold" font-size="20px" font-family="Roboto, sans-serif"><tspan x="15.2" y="34" dominant-baseline="ideographic">Obligations fiscales et sociales pour les travailleurs indépendants</tspan></text></g></g></g><g id="g-root-tx_cotisati_8x5nzgwrx3be-fill" data-item-order="0" transform="translate(62.0028076171875, 97.998046875)"><g id="tx_cotisati_8x5nzgwrx3be-fill" stroke="none" fill="#1eabda"><g><text style="font: bold 20px Roboto, sans-serif; white-space: pre;" font-weight="bold" font-size="20px" font-family="Roboto, sans-serif"><tspan x="13.67" y="34" dominant-baseline="ideographic">Cotisations </tspan><tspan x="41.55" y="58" dominant-baseline="ideographic">sociales</tspan></text></g></g></g><g id="g-root-tx_dclarati_1cw5o6kwrx3wj-fill" data-item-order="0" transform="translate(591.0028076171875, 97.998046875)"><g id="tx_dclarati_1cw5o6kwrx3wj-fill" stroke="none" fill="#3cc583"><g><text style="font: bold 20px Roboto, sans-serif; white-space: pre;" font-weight="bold" font-size="20px" font-family="Roboto, sans-serif"><tspan x="12" y="34" dominant-baseline="ideographic">Déclaration de </tspan><tspan x="12" y="58" dominant-baseline="ideographic">revenus</tspan></text></g></g></g><g id="g-root-tx_lescontr_8dsj0wsi686-fill" data-item-order="0" transform="translate(14.0028076171875, 157.998046875)"><g id="tx_lescontr_8dsj0wsi686-fill" stroke="none" fill="#484848"><g><text style="font: 15px Roboto, sans-serif; white-space: pre;" font-size="15px" font-family="Roboto, sans-serif"><tspan x="20.86" y="28" dominant-baseline="ideographic">Les contributions aux </tspan><tspan x="11.59" y="46" dominant-baseline="ideographic">prestations de sécurité </tspan><tspan x="16.98" y="64" dominant-baseline="ideographic">sociale basées sur les </tspan><tspan x="111.81" y="82" dominant-baseline="ideographic">revenus</tspan></text></g></g></g><g id="g-root-hand_4ikujwwrzwg7-fill" data-item-order="0" transform="translate(218.0028076171875, 147.412109375)"></g><g id="g-root-docu_90w90swsgrgi-fill" data-item-order="0" transform="translate(495.0028076171875, 147.412109375)"></g><g id="g-root-tx_leproces_1cuwq0cwrx1qg-fill" data-item-order="0" transform="translate(591.0028076171875, 157.998046875)"><g id="tx_leproces_1cuwq0cwrx1qg-fill" stroke="none" fill="#484848"><g><text style="font: 15px Roboto, sans-serif; white-space: pre;" font-size="15px" font-family="Roboto, sans-serif"><tspan x="12" y="31" dominant-baseline="ideographic">Le processus de </tspan><tspan x="12" y="49" dominant-baseline="ideographic">déclaration des revenus </tspan><tspan x="12" y="67" dominant-baseline="ideographic">annuels pour les </tspan><tspan x="12" y="85" dominant-baseline="ideographic">travailleurs </tspan><tspan x="12" y="103" dominant-baseline="ideographic">indépendants</tspan></text></g></g></g><g id="g-root-id-1_hznt0swsi3v1-fill" data-item-order="0" transform="translate(332.0028076171875, 199.998046875)"></g><g id="g-root-id-2_142dwvgws8cng-fill" data-item-order="0" transform="translate(357.0028076171875, 373.998046875)"></g><g id="g-root-tx_tva_m8m6cswrx1qh-fill" data-item-order="0" transform="translate(356.0028076171875, 481.998046875)"><g id="tx_tva_m8m6cswrx1qh-fill" stroke="none" fill="#92bd39"><g><text style="font: bold 20px Roboto, sans-serif; white-space: pre;" font-weight="bold" font-size="20px" font-family="Roboto, sans-serif"><tspan x="14.83" y="34" dominant-baseline="ideographic">TVA</tspan></text></g></g></g><g id="g-root-tx_lobligat_959qy4wsi6tr-fill" data-item-order="0" transform="translate(296.0028076171875, 517.998046875)"><g id="tx_lobligat_959qy4wsi6tr-fill" stroke="none" fill="#484848"><g><text style="font: 15px Roboto, sans-serif; white-space: pre;" font-size="15px" font-family="Roboto, sans-serif"><tspan x="17.07" y="28" dominant-baseline="ideographic">L'obligation de facturer </tspan><tspan x="15.99" y="46" dominant-baseline="ideographic">et de reverser la TVA si </tspan><tspan x="30.26" y="64" dominant-baseline="ideographic">le chiffre d'affaires </tspan><tspan x="36.34" y="82" dominant-baseline="ideographic">dépasse un seuil</tspan></text></g></g></g><g id="g-root-id-0_cu_sy_v9v3qkws8bgr-stroke" data-item-order="-129086" transform="translate(261.00250244140625, 127.998046875)"><g id="id-0_cu_sy_v9v3qkws8bgr-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 97.0977 245.4333C 46.8199 231.1299 10 184.8648 10 130C 10 117.627 11.8726 105.6915 15.3498 94.4612M 161.337 245.8671C 212.4141 232.0866 250 185.4326 250 130C 250 117.6953 248.148 105.8231 244.7076 94.647M 219.4653 50.0235C 197.4918 25.4599 165.5513 10 130 10C 94.1819 10 62.0291 25.6927 40.0418 50.5779"></path></g></g></g><g id="g-root-id-3_cu_sy_4nkk30ws8bgs-stroke" data-item-order="-129082" transform="translate(193.56597900390625, 121.3961181640625)"><g id="id-3_cu_sy_4nkk30ws8bgs-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 104.373597 45.58892C 101.195221 33.722816 93.490113 23.069221 82.011506 16.440818C 59.054505 3.184021 29.69751 11.047622 16.440812 34.004524C 3.184021 56.961509 11.047623 86.318519 34.004517 99.575218C 45.483002 106.203621 58.561524 107.551918 70.427521 104.373512L 116.793 91.954414L 104.376404 45.599617C 104.375519 45.596016 104.374512 45.592521 104.373597 45.58892Z"></path></g></g></g><g id="g-root-id-2_cu_sy_18mkka4ws8c1x-stroke" data-item-order="-129078" transform="translate(332.003173828125, 331.5283203125)"><g id="id-2_cu_sy_18mkka4ws8c1x-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 91.940796 43.941101C 100.627289 52.62741 106 64.627594 106 77.882507C 106 104.392211 84.509705 125.882507 58 125.882507C 31.490296 125.882507 10 104.392211 10 77.882507C 10 64.627685 15.372589 52.627716 24.058899 43.941406L 57.999695 10L 91.933106 43.933288C 91.935608 43.935913 91.938202 43.938507 91.940796 43.941101Z"></path></g></g></g><g id="g-root-id-1_cu_sy_1qf3iy4ws8buu-stroke" data-item-order="-129074" transform="translate(461.0028076171875, 121.3961181640625)"><g id="id-1_cu_sy_1qf3iy4ws8buu-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 22.423309 45.585144C 25.602692 33.719238 33.308716 23.066345 44.787903 16.438842C 67.745911 3.183944 97.102173 11.049942 110.357117 34.008041C 123.611877 56.966033 115.745911 86.322433 92.787781 99.57724C 81.308777 106.204635 68.230225 107.551834 56.364502 104.372436L 10 91.949539L 22.420502 45.595733C 22.421387 45.592239 22.422394 45.588638 22.423309 45.585144Z"></path></g></g></g><g id="g-root-tx_obligati_zitdwsws9qfb-stroke" data-item-order="0" transform="translate(93.0028076171875, 37.998046875)"></g><g id="g-root-tx_cotisati_8x5nzgwrx3be-stroke" data-item-order="0" transform="translate(62.0028076171875, 97.998046875)"></g><g id="g-root-tx_dclarati_1cw5o6kwrx3wj-stroke" data-item-order="0" transform="translate(591.0028076171875, 97.998046875)"></g><g id="g-root-tx_lescontr_8dsj0wsi686-stroke" data-item-order="0" transform="translate(14.0028076171875, 157.998046875)"></g><g id="g-root-hand_4ikujwwrzwg7-stroke" data-item-order="0" transform="translate(218.0028076171875, 147.412109375)"><g id="hand_4ikujwwrzwg7-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#969696" stroke-width="2"><g><path d="M 46 20C 46 19.447716 45.552284 19 45 19L 38 19L 38 12C 38 11.447716 37.552284 11 37 11L 31 11C 30.447716 11 30 11.447716 30 12L 30 19L 23 19C 22.447716 19 22 19.447716 22 20L 22 26C 22 26.552284 22.447716 27 23 27L 30 27L 30 34C 30 34.552284 30.447716 35 31 35L 37 35C 37.552284 35 38 34.552284 38 34L 38 27L 45 27C 45.552284 27 46 26.552284 46 26ZM 17 57L 12.98 52C 11.693836 50.408497 10.9884 48.426224 10.98 46.380001L 10.98 36C 10.979982 35.200874 11.298797 34.434765 11.865739 33.871578C 12.432681 33.308388 13.200891 32.994675 14 33L 14 33C 15.656855 33 17 34.343147 17 36L 17 43.740002M 26 56.720001L 26 50.720001C 26.003548 48.945229 25.47401 47.210293 24.48 45.739998L 22.200001 42.299999C 21.800718 41.636154 21.145927 41.165794 20.389343 40.999344C 19.632763 40.832893 18.841024 40.98502 18.200001 41.419998L 18.200001 41.419998C 17.030144 42.206562 16.62746 43.740185 17.26 45L 20 49.5M 51 57L 55 51.959999C 56.286163 50.3685 56.9916 48.386223 57 46.34L 57 36C 57 34.343147 55.656857 33 54 33L 54 33C 52.343147 33 51 34.343147 51 36L 51 43.759998M 42 56.720001L 42 50.720001C 41.996452 48.945229 42.52599 47.210293 43.52 45.739998L 45.799999 42.299999C 46.199284 41.636154 46.854073 41.165794 47.610657 40.999344C 48.367237 40.832893 49.158974 40.98502 49.799999 41.419998L 49.799999 41.419998C 50.969856 42.206562 51.37254 43.740185 50.740002 45L 48 49.5"></path></g></g></g><g id="g-root-docu_90w90swsgrgi-stroke" data-item-order="0" transform="translate(495.0028076171875, 147.412109375)"><g id="docu_90w90swsgrgi-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#969696" stroke-width="2"><g><path d="M 26 46L 42.5 46M 26 38L 26 28M 24 28L 28 28M 32 38L 32 30C 32 28.89543 32.895432 28 34 28C 35.104568 28 36 28.89543 36 30L 36 38M 32 34L 36 34M 40 28L 44 38M 44 28L 40 38M 51 57C 52.104568 57 53 56.104568 53 55L 53 22.950001C 53 16.350197 47.649803 11 41.049999 11L 17 11C 15.895431 11 15 11.895431 15 13L 15 55C 15 56.104568 15.895431 57 17 57ZM 43 11.16L 43 19C 43 20.104568 43.895432 21 45 21L 52.84 21"></path></g></g></g><g id="g-root-tx_leproces_1cuwq0cwrx1qg-stroke" data-item-order="0" transform="translate(591.0028076171875, 157.998046875)"></g><g id="g-root-id-1_hznt0swsi3v1-stroke" data-item-order="0" transform="translate(332.0028076171875, 199.998046875)"><g id="id-1_hznt0swsi3v1-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 46.124001 69.875999C 38.324986 77.679977 25.675014 77.679977 17.875999 69.875999L 14 66L 50 66ZM 98.124001 69.875999C 90.324989 77.679977 77.675018 77.679977 69.875999 69.875999L 66 66L 102 66ZM 32 75.727997C 31.876106 80.564278 35.040051 84.871719 39.692001 86.199997L 46.928001 88.267998M 84 75.727997C 84.124382 80.563156 80.962357 84.870216 76.311996 86.199997L 69.076004 88.267998M 23 22.996C 23 27.966562 27.029438 31.996 32 31.996C 36.970562 31.996 41 27.966562 41 22.996C 41 18.025436 36.970562 13.996 32 13.996C 27.029438 13.996 23 18.025436 23 22.996ZM 32 36C 20 36 20 45.372002 20 52L 20 52L 26 52L 28 66L 36 66L 38 52L 44 52L 44 52C 44 45.363998 44 36 32 36ZM 74.316002 98.963997L 74.372002 99.068001C 74.698715 99.688332 74.67675 100.434448 74.31411 101.034485C 73.951469 101.634521 73.301109 102.000854 72.599998 102L 43.400002 102C 42.697708 102.001595 42.04604 101.63472 41.683151 101.033455C 41.320259 100.432182 41.299324 99.684631 41.627998 99.064003L 41.683998 98.959999L 48.903999 84.239998C 49.578144 82.867989 50.975319 82.000191 52.504002 82.003998L 63.52 82.003998C 65.048676 82.000191 66.445854 82.867989 67.119995 84.239998ZM 94 26L 87.199997 26C 84.896461 25.98498 82.857582 27.487312 82.189545 29.691908C 81.521515 31.896505 82.383568 34.27787 84.307999 35.543999L 91.667999 40.452C 93.592476 41.714378 94.458153 44.091682 93.796043 46.295956C 93.133926 48.500229 91.101555 50.007019 88.799995 50L 82 50M 90 26.004L 90 22.004M 86 54.004002L 86 50.004002"></path></g></g></g><g id="g-root-id-2_142dwvgws8cng-stroke" data-item-order="0" transform="translate(357.0028076171875, 373.998046875)"><g id="id-2_142dwvgws8cng-stroke" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="4" stroke="#484848" stroke-width="2"><g><path d="M 31 31L 13 31C 11.895431 31 11 30.10457 11 29L 11 13C 11 11.895431 11.895431 11 13 11L 45 11C 46.104568 11 47 11.895431 47 13L 47 23M 11.008 18L 18.007999 11M 47.007999 18L 40.007999 11M 11.008 24L 18.007999 31M 33 17L 26.691999 17C 25.844688 17.007654 25.134691 17.642994 25.033333 18.484257C 24.931976 19.32552 25.470753 20.111315 26.292 20.32L 31.74 21.68C 32.561249 21.888685 33.100021 22.67448 32.998665 23.515743C 32.897308 24.357006 32.187309 24.992346 31.34 25L 25 25M 29.007999 17L 29.007999 15M 29.007999 27L 29.007999 25M 39.007999 41L 41.007999 41M 45.007999 41L 47.007999 41M 51.007999 41L 53.007999 41M 39.007999 45L 41.007999 45M 45.007999 45L 47.007999 45M 51.007999 45L 53.007999 45M 39.007999 49L 41.007999 49M 45.007999 49L 47.007999 49M 51.007999 49L 53.007999 49M 39.007999 53L 41.007999 53M 45.007999 53L 47.007999 53M 51.007999 53L 53.007999 53M 39.007999 27L 53.007999 27C 53.007999 27 57.007999 27 57.007999 31L 57.007999 53C 57.007999 53 57.007999 57 53.007999 57L 39.007999 57C 39.007999 57 35.007999 57 35.007999 53L 35.007999 31C 35.007999 31 35.007999 27 39.007999 27M 35.007999 37L 57.007999 37M 13.007999 35L 31.007999 35M 15.007999 39L 31.007999 39"></path></g></g></g><g id="g-root-tx_tva_m8m6cswrx1qh-stroke" data-item-order="0" transform="translate(356.0028076171875, 481.998046875)"></g><g id="g-root-tx_lobligat_959qy4wsi6tr-stroke" data-item-order="0" transform="translate(296.0028076171875, 517.998046875)"></g></g></g><path id="w1lr60gdehfyte" d="M65.283 12.757a.35.35 0 0 0 .584.157l5.203-5.141-6.183 3.523.396 1.461zm-2.216-11.7a.35.35 0 0 0-.522.305v3.111l3.276-1.868-2.754-1.548zm3.728 2.105l-4.25 2.421v2.445l6.391-3.644-2.141-1.222zm4.708 3.303a.35.35 0 0 0 0-.609l-1.592-.9-7.365 4.199v1.782a.35.35 0 0 0 .523.305l8.435-4.777z M44.542 2.513c0-.433.355-.783.792-.783s.792.35.792.783-.355.783-.792.783-.792-.35-.792-.783zm59.171 0c0-.433.355-.783.792-.783s.792.35.792.783-.355.783-.792.783-.792-.35-.792-.783zm-85.951 7.636h-1.27v-.487c-.276.201-.864.609-1.881.609-1.202 0-2.274-.794-2.137-2.078.118-1.106 1.153-1.584 1.848-1.727l2.17-.345c0-.539-.29-.956-1.064-1.006s-1.21.305-1.571 1.017l-1.124-.605c1.218-2.631 5.029-1.764 5.029.414v4.207zm-1.27-2.86c.006.396-.062 1.112-.819 1.59-.587.37-1.841.299-1.903-.395-.049-.555.461-.791.906-.898l1.816-.297zm72.662 2.86h-1.27v-.487c-.276.201-.864.609-1.881.609-1.202 0-2.274-.794-2.137-2.078.118-1.106 1.153-1.584 1.848-1.727l2.17-.345c0-.539-.29-.956-1.064-1.006s-1.21.305-1.571 1.017l-1.124-.605c1.218-2.631 5.029-1.764 5.029.414v4.207zm-1.27-2.86c.006.396-.062 1.112-.82 1.59-.587.37-1.841.299-1.903-.395-.049-.555.461-.791.906-.898l1.816-.297zM99.096 10.149H97.85v-8.45h1.246v4.895l2.68-2.559h1.738l-2.633 2.535 2.715 3.578h-1.556l-2.077-2.707-.867.844v1.863zm6.053-6.114h-1.255v6.113h1.255V4.035zm-59.2 0h-1.255v6.113h1.255V4.035zm5.584 6.113V1.697h1.255v2.695c.361-.346 1-.485 1.47-.485 1.452 0 2.477 1.082 2.457 2.448v3.792h-1.27v-3.68c0-.408-.214-1.339-1.315-1.339-.968 0-1.342.756-1.342 1.339v3.681h-1.255zm-4.76-4.894V4.039h.621a.45.45 0 0 0 .45-.45v-.855h1.251v1.305h1.309v1.215h-1.309v3.109c0 .293.105.664.648.664.365 0 .531-.035.736-.07v1.137s-.361.113-.857.113c-1.398 0-1.777-1.051-1.777-1.788V5.254h-1.071zM36.528 4.039h-1.394l2.191 6.106h1.125l1.234-3.918 1.238 3.918h1.129l2.188-6.106h-1.383l-1.359 3.93-1.256-3.93h-1.124l-1.242 3.957-1.348-3.957zM26.212 7.141c-.02 1.566 1.187 3.129 3.223 3.129 1.566 0 2.383-.918 2.734-1.719L31.172 8c-.315.399-.801 1.094-1.738 1.094-1.145 0-1.825-.781-1.891-1.52h4.625c.074-.284.148-.995-.03-1.559-.336-1.064-1.221-2.102-2.839-2.102s-3.088 1.152-3.088 3.227zm1.363-.75h3.348c-.055-.43-.566-1.301-1.623-1.301a1.79 1.79 0 0 0-1.725 1.301zm-8.758.75c.038 1.758 1.277 3.133 3.145 3.133 1.074 0 1.723-.477 1.961-.672v.547h1.242V1.703h-1.258v2.888c-.414-.36-1.062-.68-1.93-.68-1.91 0-3.198 1.473-3.16 3.23zm1.309-.08c0 1.119.723 1.978 1.836 1.978a1.88 1.88 0 0 0 1.94-1.904c0-1.371-1.011-1.99-1.972-1.99s-1.805.798-1.805 1.916zm76.683-.028C96.771 5.275 95.532 3.9 93.664 3.9c-1.074 0-1.723.477-1.961.672v-.547h-1.242v8.22h1.258V9.583c.414.36 1.063.68 1.93.68 1.91 0 3.198-1.473 3.16-3.23zm-1.309.08c0-1.119-.723-1.978-1.836-1.978a1.88 1.88 0 0 0-1.94 1.904c0 1.371 1.011 1.99 1.972 1.99S95.5 8.231 95.5 7.113zM106.441 10.173V4.036h1.254v.382c.361-.346 1-.485 1.47-.485 1.452 0 2.477 1.082 2.457 2.448v3.792h-1.27V6.492c0-.408-.214-1.339-1.315-1.339-.969 0-1.342.756-1.342 1.339v3.681h-1.254zm-30.383-.021V1.824h1.084l4.215 5.777V1.824h1.32v8.328h-1.094l-4.207-5.796v5.796h-1.319zM5.24 10.149H4V2.377h1.014l2.664 3.597 2.654-3.592h1.03v7.766h-1.256V4.762L7.678 8.068 5.24 4.742v5.407z" transform="translate(657, 623.998046875)" fill="#80808088" stroke="none"></path></svg>