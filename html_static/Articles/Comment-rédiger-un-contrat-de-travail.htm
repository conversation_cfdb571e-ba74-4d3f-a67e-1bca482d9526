<!DOCTYPE html>
<html lang="fr" class="h-full">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rédiger un Contrat de Travail en France : Guide Complet et Clauses Essentielles</title>
    <meta name="description"
        content="Apprenez à rédiger un contrat de travail conforme à la législation française. Ce guide détaillé couvre l'identification des parties, la description du poste, la rémunération, les clauses spécifiques (non-concurrence, confidentialité), les obligations, et les modalités de rupture. Optimisez vos contrats avec QuestionLegale.info.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="rédiger contrat travail, contrat de travail France, droit du travail, clauses contrat travail, CDI, CDD, rémunération, temps de travail, obligations employeur salarié, rupture contrat, non-concurrence, confidentialité, guide juridique, QuestionLegale.info, modèle contrat travail">

    <meta property="og:title" content="Rédiger un Contrat de Travail en France : Guide Complet et Clauses Essentielles">
    <meta property="og:description"
        content="Apprenez à rédiger un contrat de travail conforme à la législation française. Ce guide détaillé couvre l'identification des parties, la description du poste, la rémunération, les clauses spécifiques (non-concurrence, confidentialité), les obligations, et les modalités de rupture. Optimisez vos contrats avec QuestionLegale.info.">
    <meta property="og:image" content="../img/logos/main1.png">
    <meta property="og:url"
        content="https://www.questionlegale.info/Articles/Comment-rédiger-un-contrat-de-travail.htm">
    <meta property="og:type" content="article">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="../js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="../img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="../img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="../img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="../img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="../img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="../img/favicons/browserconfig.xml">
    <meta name="msapplication-TileImage" content="../img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="../img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="../img/favicons/logo.png">


    <style>
        /* ========================================
   Enhanced Blog Content Styles
   ======================================== */

        /* Modern CSS Custom Properties for Consistent Theming */
        :root {
            --blog-primary-color: #1a1a2e;
            --blog-secondary-color: #4a5568;
            --blog-accent-color: #667eea;
            --blog-text-color: #2d3748;
            --blog-light-bg: #f8fafc;
            --blog-hover-bg: #edf2f7;
            --blog-border-color: #e2e8f0;
            --blog-spacing-xs: 0.5rem;
            --blog-spacing-sm: 1rem;
            --blog-spacing-md: 1.5rem;
            --blog-spacing-lg: 2rem;
            --blog-spacing-xl: 2.5rem;
            --blog-border-radius: 0.5rem;
            --blog-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Base Blog Content Container */
        /* ========================================
   Enhanced Blog Content Styles
   ======================================== */


        /* Base Blog Content Container */
        .blog-content {
            max-width: 65ch;
            /* Optimal reading width */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--blog-text-color);
            line-height: 1.7;
        }

        /* Enhanced Image Styles */
        .blog-content img {
            max-width: 100%;
            height: auto;
            margin: var(--blog-spacing-xl) 0;
            border-radius: var(--blog-border-radius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: var(--blog-transition);
        }

        .blog-content img:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Improved Typography */
        .blog-content p {
            margin-bottom: var(--blog-spacing-md);
            line-height: 1.2;
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* Modern Heading Styles */
        .blog-content h2 {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            /* Responsive sizing */
            font-weight: 800;
            margin: var(--blog-spacing-xl) 0 var(--blog-spacing-md);
            color: var(--blog-primary-color);
            letter-spacing: -0.025em;
            line-height: 1.2;
            position: relative;
            padding-bottom: var(--blog-spacing-sm);
        }

        /* Modern gradient underline instead of solid border */
        .blog-content h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--blog-accent-color), transparent);
            border-radius: 2px;
            transition: var(--blog-transition);
        }

        .blog-content h2:hover::after {
            width: 100px;
        }

        .blog-content h3 {
            font-size: clamp(1.25rem, 3vw, 1.75rem);
            font-weight: 600;
            margin: var(--blog-spacing-lg) 0 0.75rem;
            color: var(--blog-secondary-color);
            letter-spacing: -0.01em;
            line-height: 1.1;
        }

        /* Clean and Professional List Styles */
        .blog-content ul {
            list-style: none;
            padding-left: 0;
            margin: var(--blog-spacing-md) 0;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .blog-content ul li {
            position: relative;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: #ffffff;
            font-size: 1.05rem;
            color: var(--blog-text-color);
            display: flex;
            align-items: flex-start;
            line-height: 1.1;
            font-weight: 400;
        }

        /* Professional checkmark */
        .blog-content ul li::before {
            content: '✓';
            position: absolute;
            left: 0.75rem;
            top: 0.9rem;
            width: 1rem;
            height: 1rem;
            background: var(--blog-accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.7rem;
            flex-shrink: 0;
        }

        /* Focus States for Accessibility */
        .blog-content ul li:focus-within {
            outline: 2px solid var(--blog-accent-color);
            outline-offset: 2px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            :root {
                --blog-spacing-xl: 1.5rem;
                --blog-spacing-lg: 1.25rem;
                --blog-spacing-md: 1rem;
            }

            .blog-content {
                padding: 0 var(--blog-spacing-sm);
            }

            .blog-content p {
                font-size: 1rem;
                line-height: 1.7;
            }

            .blog-content ul {
                gap: 0.2rem;
            }

            .blog-content ul li {
                padding: 0.6rem 0.75rem 0.6rem 2.25rem;
                font-size: 1rem;
            }

            .blog-content ul li::before {
                left: 0.6rem;
                top: 0.75rem;
                width: 0.9rem;
                height: 0.9rem;
                font-size: 0.65rem;
            }
        }

        @media (max-width: 480px) {
            .blog-content ul li {
                padding: var(--blog-spacing-xs) var(--blog-spacing-xs) var(--blog-spacing-xs) 2.25rem;
                font-size: 0.95rem;
            }

            .blog-content ul li::before {
                left: var(--blog-spacing-xs);
            }
        }

        /* Conseil Pratique Box */
        .conseil-pratique {
            background-color: #e0f2fe;
            /* Light blue background */
            border-left: 5px solid #3b82f6;
            /* Blue left border */
            padding: var(--blog-spacing-md);
            margin: var(--blog-spacing-xl) 0;
            border-radius: var(--blog-border-radius);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: flex-start;
            gap: var(--blog-spacing-sm);
        }

        .conseil-pratique .icon {
            color: #3b82f6;
            /* Blue icon */
            font-size: 1.8rem;
            flex-shrink: 0;
        }

        .conseil-pratique p {
            margin: 0;
            font-size: 1rem;
            line-height: 1.6;
            color: #1e3a8a;
            /* Darker blue text */
        }

        .conseil-pratique p strong {
            color: #1e3a8a;
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: #3B82F6;
            z-index: 50;
            transition: width 0.2s ease;
        }

        .image-hover-zoom {
            overflow: hidden;
        }

        .image-hover-zoom img {
            transition: transform 0.5s ease;
        }

        .group:hover .image-hover-zoom img {
            transform: scale(1.1);
        }

        /* Placeholder image styles */
        .placeholder {
            position: relative;
            background: #f3f4f6;
            overflow: hidden;
        }

        .placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            100% {
                left: 100%;
            }
        }


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }


        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }


        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }

        /* Arrow animation for links */
        .animated-arrow-link {
            display: flex;
            align-items: center;
            transition: color 0.3s ease;
        }

        .animated-arrow-link .arrow-icon {
            margin-left: 8px;
            transition: transform 0.3s ease;
        }

        .animated-arrow-link:hover .arrow-icon {
            transform: translateX(5px);
        }

        /* SVG styling */
        .article-svg {
            width: 100%;
            height: auto;
            background-color: white;
            padding: 10px;
            /* Optional: add some padding around the SVG */
            box-sizing: border-box;
            /* Include padding in the width */
        }
    </style>
</head>


<body class="bg-gray-200 text-gray-800 h-full">
    <div class="reading-progress" id="reading-progress"></div>

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="../index.html" tooltip="Question Legale" ><div class="flex items-center space-x-2">
            <!-- Icon: replace emoji with SVG or image if needed -->
            <img src="../img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
            <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>

    </div></a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <button id="mobileSignIn" type="button"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap">
                Se Connecter
            </button>
            <button id="mobileSignUp" type="button"
                class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap">
                Inscription
            </button>
            <!-- Burger menu button -->
            <button id="burgerBtn" type="button" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="../#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="articleslist.html"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="../#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <button id="signInBtn" type="button"
                class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                Se Connecter
            </button>
            <button id="signUpBtn" type="button"
                class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap">
                Inscription
            </button>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="../#home" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question</a>
            <a href="../#about" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="../services.html" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Services</a>
            <a href="../#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
            <a href="../#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
        </div>
        <!-- Buttons in mobile menu -->
        <div class="flex space-x-3 mt-4">
            <button type="button"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button"
                class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Inscription
            </button>
        </div>
    </div>

    <!-- Presentation -->
    <section id="home" class="relative mb-12 py-16 min-h-screen">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <div class="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
            <!-- Main Content -->
            <main class="lg:w-3/4" data-aos="fade-up" data-aos-duration="1000">
                <article class="bg-white rounded-xl shadow-md p-6 lg:p-8">
                    <header class="mb-8">
                        <h1 class="text-4xl font-bold mb-4 text-gray-900">Comment rédiger un contrat de travail ?</h1>
                        <div class="flex items-center text-gray-600 mb-4">
                            <div class="placeholder w-10 h-10 rounded-full mr-4">
                                <img src="../img/avocat1.png" alt="Auteur" class="w-10 h-10 rounded-full">
                            </div>
                            <div>
                                <p class="font-semibold">QuestionLegale.info</p>
                                <p class="text-sm">Publié le 27 mai 2025 · Lecture estimée: 10 min</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span
                                class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#ContratDeTravail</span>
                            <span
                                class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#DroitDuTravail</span>
                            <span
                                class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#GuideJuridique</span>
                        </div>
                    </header>

                    <div class="blog-content prose lg:prose-lg max-w-none">
                        <div class="placeholder rounded-xl mb-8 aspect-[2/1]">
                            <img src="../Articles/pict/Comment rédiger un contrat de travail/Designer (1).jpeg"
                                alt="Image Principale" class="rounded-xl w-full h-full object-cover">
                        </div>
                        <p>La rédaction d'un contrat de travail est une étape fondamentale, que vous soyez employeur ou
                            futur salarié.
                            <br>
                            Ce document n'est pas qu'une simple formalité : c'est la pierre angulaire de votre relation
                            professionnelle, définissant clairement les droits et les devoirs de chacun, ainsi que les
                            conditions de travail.
                            <br>
                            Dans ce guide complet, nous allons explorer ensemble tous les
                            éléments indispensables pour un contrat de travail clair, équilibré et parfaitement conforme
                            à la législation française. Prêt à maîtriser l'art de la rédaction contractuelle ?
                        <div class="flex justify-center my-8">
                            <img src="../Articles/pict/Comment rédiger un contrat de travail/Comment rédiger un contrat de travail _ - visual selection.jpg"
                                alt="Structure d'un contrat de travail" class="max-w-full h-auto">
                        </div>
                        </p>
                        <h2 id="identification-des-parties">Qui sont les acteurs ? L'identification des parties</h2>
                        <p>Pour commencer, votre contrat doit identifier sans équivoque les deux parties prenantes.
                            C'est la base pour éviter toute confusion future :</p>
                        <h3>Côté employeur :</h3>
                        <ul>
                            <li>La raison sociale complète de l'entreprise (le nom officiel)</li>
                            <li>Sa forme juridique (SARL, SA, SAS, etc.)</li>
                            <li>Son numéro SIRET (une sorte de carte d'identité de l'entreprise)</li>
                            <li>L'adresse de son siège social</li>
                            <li>Le nom et la fonction de la personne habilitée à signer le contrat (souvent le
                                dirigeant)</li>
                            <li>Le Code NAF/APE (qui indique l'activité principale de l'entreprise)</li>
                            <li>La convention collective applicable (très important, car elle complète le Code du
                                travail)</li>
                        </ul>

                        <h3>Côté salarié :</h3>
                        <ul>
                            <li>Votre nom et vos prénoms</li>
                            <li>Votre date et lieu de naissance</li>
                            <li>Votre adresse complète</li>
                            <li>Votre numéro de sécurité sociale (pour les démarches administratives et la protection
                                sociale)</li>
                            <li>Votre nationalité (si cela a une incidence sur votre droit à travailler en France)</li>
                        </ul>


                        <div class="conseil-pratique">
                            <i class="fas fa-lightbulb icon"></i>
                            <p>
                            <h3 nowrap>Conseil Pratique:</h3> Assurez-vous que toutes les informations sont exactes et à
                            jour. Une erreur, même minime, peut entraîner des complications juridiques. Prenez le temps
                            de vérifier chaque détail !</p>
                        </div>

                        <h2 id="description-du-poste">Quel sera votre rôle ? La description du poste</h2>
                        <p>Cette section est essentielle pour que chacun sache précisément ce qu'il doit faire. Elle
                            doit décrire en détail le poste que le salarié va occuper :</p>
                        <ul>
                            <li>L'intitulé exact du poste (par exemple, "Développeur Web Junior", "Responsable
                                Marketing")</li>
                            <li>La classification du poste selon la convention collective (cela détermine souvent le
                                salaire minimum et d'autres avantages)</li>
                            <li>Les missions et responsabilités principales (décrivez ce que le salarié fera au
                                quotidien)</li>
                            <li>Les compétences et qualifications requises (diplômes, expériences spécifiques)</li>
                            <li>Votre position dans l'organigramme et vos liens avec les autres services ou managers
                            </li>
                            <li>Le ou les lieux où le travail sera effectué (et si vous devrez vous déplacer
                                régulièrement)</li>
                        </ul>

                        <h2 id="duree-et-nature-du-contrat">Pour combien de temps ? Durée et nature du contrat</h2>
                        <p>C'est ici que vous définissez le type de contrat et sa durée. C'est une distinction
                            fondamentale en droit du travail français :</p>
                        <h3>Pour un CDI (Contrat à Durée Indéterminée) :</h3>
                        <ul>
                            <li>La date de début de votre aventure dans l'entreprise</li>
                            <li>La durée et les conditions de la période d'essai (et comment elle peut être renouvelée,
                                si c'est prévu)</li>
                        </ul>

                        <h3>Pour un CDD (Contrat à Durée Déterminée) :</h3>
                        <ul>
                            <li>Les dates précises de début et de fin du contrat</li>
                            <li>Le motif légal précis qui justifie ce CDD (remplacement d'un salarié absent, surcroît
                                d'activité, etc. – les motifs sont strictement encadrés par la loi !)</li>
                            <li>Le nom du salarié remplacé (si c'est un remplacement)</li>
                            <li>Les modalités de renouvellement éventuel</li>
                            <li>L'indemnité de fin de contrat, aussi appelée "prime de précarité" (une compensation pour
                                la nature temporaire du contrat)</li>
                        </ul>



                        <h2 id="remuneration-et-avantages">Combien gagnerez-vous ? Rémunération et avantages</h2>
                        <p>L'argent, c'est important ! Cette section doit être d'une clarté absolue pour éviter tout
                            malentendu :</p>
                        <ul>
                            <li>Le montant de votre salaire brut (par heure, par mois ou par an)</li>
                            <li>Le mode de calcul de votre salaire (fixe, avec une part variable, au rendement)</li>
                            <li>Les composantes variables de votre rémunération (commissions, primes sur objectifs,
                                intéressement aux résultats de l'entreprise)</li>
                            <li>Le calendrier et les modalités de versement (quand et comment vous serez payé)</li>
                        </ul>
                        <h3>Les avantages complémentaires :</h3>
                        <ul>
                            <li>Les primes éventuelles (13ème mois, primes de vacances, d'ancienneté)</li>
                            <li>Les avantages en nature (voiture de fonction, logement, téléphone professionnel – leur
                                valeur doit être estimée)</li>
                            <li>Les tickets restaurant, la participation aux frais de transport</li>
                            <li>Les régimes de prévoyance (assurance en cas d'incapacité, invalidité, décès) et de
                                complémentaire santé (mutuelle)</li>
                            <li>Les dispositifs d'épargne salariale (plan d'épargne entreprise, participation,
                                intéressement)</li>
                        </ul>


                        <h2 id="temps-de-travail-et-organisation">Votre emploi du temps : Temps de travail et
                            organisation</h2>
                        <p>Cette partie détaille comment votre temps de travail sera organisé. C'est crucial pour votre
                            équilibre vie pro/vie perso :</p>
                        <ul>
                            <li>La durée hebdomadaire ou mensuelle de travail (par exemple, 35 heures par semaine)</li>
                            <li>Vos horaires précis ou les plages horaires (si vos horaires sont fixes)</li>
                            <li>Les modalités spécifiques (travail en équipes, astreintes, travail à temps partiel)</li>
                            <li>Comment seront traitées les heures supplémentaires ou complémentaires (rémunération,
                                repos compensateur)</li>
                            <li>Vos jours de repos hebdomadaire et autres jours de repos</li>
                            <li>Vos congés payés et les règles pour les poser</li>
                            <li>Les jours fériés travaillés ou chômés</li>
                        </ul>

                        <h2 id="clauses-specifiques">Les clauses "spéciales" : Clauses spécifiques</h2>
                        <p>Certains postes ou secteurs d'activité nécessitent des clauses particulières. Elles sont là
                            pour protéger les intérêts de l'entreprise, mais aussi pour encadrer vos obligations :</p>
                        <h3>La clause de non-concurrence :</h3>
                        <ul>
                            <li>Sa durée et la zone géographique où elle s'applique</li>
                            <li>Les activités que vous ne pourrez pas exercer après votre départ</li>
                            <li>La contrepartie financière que l'employeur doit vous verser en échange de cette
                                restriction</li>
                            <li>Les conditions dans lesquelles l'employeur peut y renoncer</li>
                        </ul>
                        <h3>La clause de confidentialité :</h3>
                        <ul>
                            <li>La nature des informations que vous devez garder secrètes</li>
                            <li>La durée de cette obligation (pendant et après le contrat)</li>
                            <li>Les sanctions si vous ne respectez pas cette clause</li>
                        </ul>
                        <h3>La clause de mobilité :</h3>
                        <ul>
                            <li>La zone géographique précise où l'employeur peut vous demander de travailler</li>
                            <li>Les modalités d'application (préavis, accompagnement éventuel)</li>
                        </ul>

                        <h3>La clause d'exclusivité (si vous ne pouvez travailler que pour cet employeur)</h3>
                        <h3>La clause de propriété intellectuelle (très importante pour les créatifs et les innovateurs)
                        </h3>
                        <h3>La clause de télétravail (si vous travaillez à distance : modalités, équipements, plages de
                            disponibilité)</h3>


                        <h2 id="obligations-des-parties">Les devoirs de chacun : Obligations des parties</h2>
                        <p>Un contrat, c'est un engagement mutuel. Cette section rappelle les obligations fondamentales
                            de l'employeur et du salarié :</p>
                        <h3>Les obligations de l'employeur :</h3>
                        <ul>
                            <li>Fournir le travail convenu et les moyens de l'exécuter</li>
                            <li>Respecter toutes les lois et la convention collective</li>
                            <li>Assurer votre sécurité et protéger votre santé au travail</li>
                            <li>Vous former et vous accompagner dans l'évolution de votre poste</li>
                        </ul>
                        <h3>Les obligations du salarié :</h3>
                        <ul>
                            <li>Exécuter votre travail en suivant les directives de l'employeur</li>
                            <li>Respecter les règles d'hygiène et de sécurité</li>
                            <li>Faire preuve de loyauté et de discrétion (ne pas divulguer d'informations sensibles)
                            </li>
                            <li>Prendre soin du matériel et des équipements mis à votre disposition</li>
                        </ul>

                        <h2 id="rupture-du-contrat">Que se passe-t-il si ça s'arrête ? La rupture du contrat</h2>
                        <p>Même si on espère que la relation sera longue, il faut prévoir la fin. Cette section détaille
                            les modalités de rupture du contrat :</p>
                        <ul>
                            <li>La durée du préavis (le délai à respecter avant le départ effectif), qui varie selon le
                                motif de rupture</li>
                            <li>Les procédures de notification (comment informer l'autre partie, dans quels délais)</li>
                            <li>Les conséquences de la rupture (les indemnités auxquelles vous pourriez avoir droit)
                            </li>
                            <li>Les clauses de retour des biens de l'entreprise (ordinateur, téléphone, etc.)</li>
                        </ul>

                        <h2 id="droit-applicable-et-resolution-des-litiges">En cas de désaccord : Droit applicable et
                            résolution des litiges</h2>
                        <p>Pour éviter les mauvaises surprises, cette dernière section indique clairement les règles du
                            jeu en cas de problème :</p>
                        <ul>
                            <li>La législation qui s'applique au contrat (le droit français et la convention collective)
                            </li>
                            <li>La juridiction compétente en cas de litige (le Conseil de Prud'hommes le plus proche de
                                votre lieu de travail)</li>
                            <li>Les procédures alternatives pour régler les différends (médiation, conciliation, pour
                                trouver une solution à l'amiable)</li>
                            <li>La clause de nullité partielle (qui précise que si une clause est jugée invalide, cela
                                n'annule pas l'ensemble du contrat)</li>
                        </ul>

                        <h2 id="formalites-et-signatures">La touche finale : Formalités et signatures</h2>
                        <p>Pour que le contrat soit valide, il doit être finalisé correctement :</p>
                        <ul>
                            <li>La mention du nombre d'exemplaires originaux (au moins deux : un pour l'employeur, un
                                pour le salarié)</li>
                            <li>La date et le lieu de signature</li>
                            <li>La mention manuscrite "Lu et approuvé" suivie de la signature des deux parties (une
                                étape cruciale !)</li>
                            <li>L'indication des annexes éventuelles (fiche de poste détaillée, règlement intérieur,
                                etc.)</li>
                        </ul>

                        <h2 id="conclusion">Conclusion : Un contrat bien ficelé, une relation sereine !</h2>
                        <p>Rédiger un contrat de travail, c'est un peu comme construire les fondations d'une maison :
                            cela demande de la rigueur, une attention aux détails et une solide connaissance des règles.
                            Pour vous assurer que tout est parfait et que vos intérêts sont bien protégés, il est
                            toujours vivement recommandé de consulter un professionnel du droit. Un contrat clair et
                            équilibré est la clé d'une relation de travail saine, transparente et durable. N'hésitez pas
                            à investir dans cette étape, c'est un gage de tranquillité pour l'avenir !</p>

                        <h2 id="recommandations">Nos dernières astuces pour un contrat au top !</h2>
                        <ul>
                            <li>Utilisez un langage simple et direct, évitez le jargon juridique complexe quand c'est
                                possible.</li>
                            <li>Vérifiez toujours la conformité avec la convention collective applicable, elle prime
                                souvent sur le Code du travail pour certains aspects.</li>
                            <li>Mettez à jour le contrat si les conditions de travail changent de manière significative.
                            </li>
                            <li>Conservez précieusement les contrats dans un endroit sûr et confidentiel.</li>
                            <li>Laissez au salarié un temps raisonnable pour lire et comprendre le contrat avant de le
                                signer.</li>
                        </ul>

                    </div>

                </article>
            </main>
            <!-- Image Placeholder -->
            <aside class="lg:w-1/3" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="bg-white rounded-xl shadow-md p-6 sticky top-20 h-fit">
                    <h2 class="text-xl font-bold mb-6 text-gray-900">Sommaire</h2>
                    <div class="space-y-6">
                        <a href="#identification-des-parties"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Qui sont les acteurs ? L'identification des parties
                        </a>
                        <a href="#description-du-poste"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Quel sera votre rôle ? La description du poste
                        </a>
                        <a href="#duree-et-nature-du-contrat"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Pour combien de temps ? Durée et nature du contrat
                        </a>
                        <a href="#remuneration-et-avantages"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Combien gagnerez-vous ? Rémunération et avantages
                        </a>
                        <a href="#temps-de-travail-et-organisation"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Votre emploi du temps : Temps de travail et organisation
                        </a>
                        <a href="#clauses-specifiques"
                            class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Les clauses "spéciales" : Clauses spécifiques
                        </a>
                        <a href="#obligations-des-parties"
                            class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Les devoirs de chacun : Obligations des parties
                        </a>
                        <a href="#rupture-du-contrat"
                            class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Que se passe-t-il si ça s'arrête ? La rupture du contrat
                        </a>
                        <a href="#droit-applicable-et-resolution-des-litiges"
                            class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            En cas de désaccord : Droit applicable et résolution des litiges
                        </a>
                        <a href="#formalites-et-signatures"
                            class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            La touche finale : Formalités et signatures
                        </a>
                        <a href="#conclusion"
                            class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300 group font-bold">
                            <i
                                class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Conclusion : Un contrat bien ficelé, une relation sereine !
                        </a>
                    </div>
                </div>
                <!-- New Sticky Panel for Advertisement -->
                <div class="bg-white rounded-xl shadow-md p-4 mt-6 text-center">
                    <a href="https://www.questionlegale.info" target="_blank" rel="noopener noreferrer">
                        <img src="../img/pub/pub1_QL.jpg" alt="Publicité QuestionLegale.info"
                            class="h-auto rounded-lg mx-auto block">
                    </a>
                </div>
            </aside>
        </div>
    </section>



    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="../img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="../#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="articleslist.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                        <li>
                            <a href="../#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="../#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                         <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                               Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|</span>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible" aria-label="Back to top">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>
</body>

</html>
