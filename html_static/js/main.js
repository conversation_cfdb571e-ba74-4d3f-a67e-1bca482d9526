//static Js : Open google maps , pages jaunes, download pdf, copy text

//Global var
var latitude, longitude, ville;

var linkAPJ , linkAGM , linkNPJ , linkNGM ;

//Build links for avocat and notaire bsee sur l'ip
// window.onload = function() 
function initStatic() {
   //Retrieve Location
   linkAPJ = document.getElementById("LinkAvocatPageJaune");
   linkAGM = document.getElementById("LinkAvocatGoogleMap");

   linkNPJ = document.getElementById("LinkNotairePageJaune");
   linkNGM = document.getElementById("LinkNotaireGoogleMap");
 
   local();
};

//Geolocalisation
function local() {
    fetch('http://ip-api.com/json/')
        .then(response => response.json())
        .then(data => {
            latitude = data.lat;
            longitude = data.lon;
            ville = data.city;
            /*   const result = document.getElementById('result');
              result.innerHTML = `
                  <h2>Location Information:</h2>
                  <p><strong>IP Address:</strong> ${data.query}</p>
                  <p><strong>City:</strong> ${data.city}</p>
                  <p><strong>Region:</strong> ${data.regionName}</p>
                  <p><strong>Country:</strong> ${data.country}</p>
                  <p><strong>Latitude:</strong> ${data.lat}</p>
                  <p><strong>Longitude:</strong> ${data.lon}</p>
                  <p><strong>ISP:</strong> ${data.isp}</p>
                  <p><strong>Timezone:</strong> ${data.timezone}</p>
              `; */
            
            if (linkAPJ) {
                //https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=plombier&ou=Nantes+%2844000%29&univers=pagesjaunes&idOu=
                //https://www.pagesjaunes.fr/carte/recherche?quoiqui=avocat&ou=Paris&univers=pagesjaunes&idOu=
                linkAPJ.href = "https://www.pagesjaunes.fr/carte/recherche?quoiqui=avocat&ou="+ville+"&univers=pagesjaunes&idOu=";
                //link.innerText = "Visit Example Website";
            }
            // Modify its href 
            if (linkAGM) {
                //https://www.google.com/maps/search/avocat+Naaldwijk
                //"https://www.google.com/maps/search/Avocat/@"+latitude+","+longitude+",14z`"
                linkAGM.href = "https://www.google.com/maps/search/avocat+"+ville;
                //link.innerText = "Visit Example Website";
            }

            if (linkNPJ) {
                linkNPJ.href = "https://www.pagesjaunes.fr/carte/recherche?quoiqui=Notaire&ou="+ville+"&univers=pagesjaunes&idOu=";
            }
            // Modify its href 
            if (linkNGM) {
                 linkNGM.href = "https://www.google.com/maps/search/Notaire+"+ville;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert( 'Erreur dans la geolocation.');
        });
};


//Generate pdf pour le contenue de la page avec un logo questionlegale  


//Verify if french langage, size of the text, 
function detectFrench(text) {
    const frenchWords = [
        'le', 'la', 'les', 'un', 'une', 'des',
        'je', 'tu', 'il', 'elle', 'nous', 'vous',
        'de', 'à', 'en', 'dans', 'sur', 'avec',
        'et', 'mais', 'ou', 'car',
        'être', 'avoir', 'faire', 'dire', 'aller'
    ];

    const frenchGrammarIndicators = [
        'é', 'è', 'ê', 'ë', 'à', 'â', 'î', 'ï', 'ô', 'ù', 'û', 'ç',
        'ais', 'ait', 'ions', 'iez', 'aient',
        '-t-il', '-t-elle', 'qu\'', 'n\'', 'm\'', 't\'', 's\'', 'c\''
    ];

    const lowerText = text.toLowerCase();

    const frenchScore = {
        commonWords: 0,
        accents: 0,
        grammarPatterns: 0
    };

    frenchWords.forEach(word => {
        if (lowerText.includes(word)) {
            frenchScore.commonWords += 1;
        }
    });

    const accentCount = (lowerText.match(/[éèêëàâîïôùûç]/g) || []).length;
    frenchScore.accents = accentCount;

    frenchGrammarIndicators.forEach(pattern => {
        if (lowerText.includes(pattern)) {
            frenchScore.grammarPatterns += 1;
        }
    });

    const totalScore =
        frenchScore.commonWords * 2 +
        frenchScore.accents * 1.5 +
        frenchScore.grammarPatterns * 3;

    return {
        isLikelyFrench: totalScore > 5,
        score: totalScore,
        details: frenchScore
    };
}

function checkFrench() {
    const text = document.getElementById('textInput').value;
    const resultDiv = document.getElementById('result');

    if (!text.trim()) {
        resultDiv.innerHTML = 'Please enter some text';
        return;
    }

    const result = detectFrench(text);

    alert(`
        <strong>Is Likely French:</strong> ${result.isLikelyFrench ? 'Yes' : 'No'}<br>
        <strong>Confidence Score:</strong> ${result.score.toFixed(2)}<br>
        <strong>Detection Details:</strong><br>
        - Common Words: ${result.details.commonWords}<br>
        - Accented Characters: ${result.details.accents}<br>
        - Grammar Patterns: ${result.details.grammarPatterns}
    `);
}
