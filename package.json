{"name": "supavite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "markdown-it": "^14.1.0", "marked": "^15.0.11", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/estree": "^1.0.7", "@types/node": "^22.15.24", "@types/ws": "^8.18.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/language-server": "^2.2.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}}